"""
Website research agent using langgraph with dedicated nodes for each research step
"""

import operator
from typing import Annotated

import yaml
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import HumanMessage
from langgraph.graph import END, StateGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Send
from loguru import logger
from pydantic import BaseModel, Field
from typing_extensions import TypedDict

from agent.tools.apollo import ApolloAPIClient
from agent.tools.firecrawl_tools import firecrawl_map, firecrawl_scrape
from utils.models import init_model


# pydantic models for structured output
class SubTask(BaseModel):
    """structure for research sub-task response"""

    task_id: int = Field(description="unique identifier for the task, e.g., 1, 2, 3")
    explanation: str = Field(description="brief explanation of the research strategy")
    description: str = Field(description="clear description of what needs to be researched")
    urls: list[str] = Field(description="list of relevant URLs for this sub-task (max 8 URLs)")


class ResearchPlan(BaseModel):
    """complete research plan with multiple sub-tasks"""

    rationale: str = Field(description="brief explanation of the research strategy")
    url_insights: str = Field(
        description="detailed description of the insights, including specific analysis, inferences, and references to relevant URLs"
    )
    sub_tasks: list[SubTask] = Field(description="list of research sub-tasks")


class Reflection(BaseModel):
    """reflection on the gathered information"""

    is_sufficient: bool = Field(
        description="whether the provided research results are sufficient to answer the research task"
    )
    knowledge_gap: str = Field(description="description of what information is missing or needs clarification")
    follow_up_tasks: list[SubTask] = Field(description="additional research sub-tasks needed, if any")


class FilteredUrls(BaseModel):
    """filtered URLs based on research task relevance"""

    url_indices: list[int] = Field(description="list of URL indices that are relevant to the research task")


# define agent state
class WebsiteResearchState(BaseModel):
    """the state of the website research agent"""

    research_task: str = Field(description="the research task")
    website_url: str = Field(description="company official website url")
    discovered_urls: list[str] = Field(default_factory=list, description="urls found during website mapping")
    url_insights: str = Field(default="", description="insights discovered from URL analysis")
    sub_tasks: list[SubTask] = Field(default_factory=list, description="decomposed sub-tasks with related urls")
    completed_tasks: Annotated[dict[int, str], operator.ior] = Field(
        default_factory=dict, description="task_id -> result mapping"
    )
    # apollo organization details
    organization_details: dict = Field(default_factory=dict, description="organization details from apollo")
    # reflection fields
    is_sufficient: bool = Field(default=False, description="whether current research is sufficient")
    knowledge_gap: str = Field(default="", description="description of missing information")
    research_loop_count: int = Field(default=0, description="number of research iterations")
    max_research_loops: int = Field(default=3, description="maximum allowed research loops")
    final_report: str = Field(default="", description="final consolidated research report")


class ScrapeTaskState(TypedDict, total=False):
    """extended state that includes current sub-task for processing"""

    search_task: SubTask  # current sub-task to process
    research_task: str  # the research task


class WebsiteResearchAgent:
    """Website research agent with dedicated nodes for each research step"""

    def __init__(self):
        # initialize model for planning and reporting
        self.query_generation_model: BaseChatModel = init_model(
            # model="gemini-2.5-pro",
            # include_thoughts=True,
            # thinking_budget=256,
            temperature=0.3,
            model="gpt-4.1",
        )

        self.finalize_model: BaseChatModel = init_model(
            model="gemini-2.5-pro",
            include_thoughts=True,
            thinking_budget=256,
            temperature=0.1,
            # model="o3"
        )

        # initialize apollo client
        self.apollo_client = ApolloAPIClient()

    async def website_map(self, state: WebsiteResearchState) -> dict:
        """Node 1: Map website to discover all page URLs"""
        logger.info(f"🗺️ mapping website: {state.website_url}")

        try:
            # use firecrawl to map website
            urls_result = await firecrawl_map.ainvoke(
                input={
                    "url": state.website_url,
                    "limit": 500,
                }
            )

            # parse URLs from result
            discovered_urls = []
            # split by newlines and filter valid URLs
            urls = urls_result.split("\n")
            discovered_urls = [url.strip() for url in urls if url.startswith("http")]

            logger.info(f"📍 discovered {len(discovered_urls)} URLs")

            # filter URLs using LLM
            if discovered_urls:
                filtered_urls = await self._filter_urls(state.research_task, discovered_urls)
                logger.info(f"🔍 filtered to {len(filtered_urls)} relevant URLs")
                return {"discovered_urls": filtered_urls}
            else:
                return {"discovered_urls": discovered_urls}

        except Exception as e:
            logger.error(f"❌ error mapping website: {e}", exc_info=True)
            return {"discovered_urls": []}

    async def apollo_research(self, state: WebsiteResearchState) -> dict:
        """Node: Get organization details from Apollo using website URL"""
        logger.info(f"🔍 searching organization details via Apollo for: {state.website_url}")
        # search organization by website URL
        search_result = await self.apollo_client.search_organization(q_organization_name=state.website_url)

        if "error" in search_result:
            logger.warning(f"❌ apollo search failed: {search_result['error']}")
            return {"organization_details": {"error": f"Error on Apollo: {search_result['error']}"}}

        # if organizations found, get detailed info for the first match
        if search_result and len(search_result) > 0:
            org_id = search_result[0].get("id")
            logger.info(f"📋 found organization {search_result[0].get('name')}, fetching detailed info")
            detailed_info = await self.apollo_client.get_organization_by_id(org_id)

            if "error" in detailed_info:
                logger.warning(f"❌ apollo detail fetch failed: {detailed_info['error']}")
                return {"organization_details": {"error": f"Error on Apollo: {detailed_info['error']}"}}

            logger.info("✅ successfully retrieved organization details")
            return {"organization_details": detailed_info}
        else:
            logger.warning("⚠️ no organizations found in apollo search")
            return {"organization_details": {"error": "No organizations found in Apollo"}}

    async def _filter_urls(self, research_task: str, urls: list[str]) -> list[str]:
        """filter URLs using LLM based on research task relevance"""
        logger.info(f"🎯 filtering {len(urls)} URLs with LLM")
        urls = sorted(urls)

        try:
            # create indexed URL list for prompt
            indexed_urls = [f"{i + 1}. {url}" for i, url in enumerate(urls)]
            indexed_url_text = chr(10).join(indexed_urls)

            # create URL filtering prompt
            filter_prompt = f"""你是专业的企业调研分析师，根据调研任务从URL列表中筛选出有价值的链接。

## 任务
根据具体调研目标，分析并筛选URL，保留对调研有价值的链接，过滤无关内容。

## 筛选原则
1. **高相关性**: 内容直接支撑调研目标
2. **高价值**: 包含关键信息或独特见解
3. **去重复**: 避免内容相似的链接
4. **排除无效**: 过滤登录页、法律条款、404等

## 筛选流程
1. 快速排除明显无关和无效链接
2. 评估剩余链接与调研任务的相关性
3. 按价值高低排序，确保保留至少50条URL

## 调研任务
```
{research_task}
```

## URL列表（共{len(urls)}个）
{indexed_url_text}

## 输出要求
请从上述URL列表中筛选出与调研任务相关的有价值链接的索引号。
输出格式：只需要输出相关URL的索引号数组（例如：[1, 5, 12, 25]）

## 注意事项
- 根据调研任务动态调整筛选标准
- 优先保留中文内容，英文内容作为补充
- 对模糊情况倾向于保留
- 过滤掉明显无关的页面（如登录页、法律条款、404等）
- 只输出索引号，不需要输出URL本身"""

            # use structured output to filter URLs
            llm: BaseChatModel = init_model(
                temperature=0.1,
                model="gpt-4.1",
            )

            filter_result = await llm.with_structured_output(FilteredUrls).ainvoke(
                [HumanMessage(content=filter_prompt)]
            )

            # extract filtered URLs using indices
            filtered_indices = filter_result.url_indices
            filtered_urls = []

            for idx in filtered_indices:
                # convert 1-based index to 0-based
                if 1 <= idx <= len(urls):
                    filtered_urls.append(urls[idx - 1])
                else:
                    logger.warning(f"⚠️ invalid URL index: {idx}")

            logger.info(f"✅ filtered {len(urls)} URLs down to {len(filtered_urls)} relevant URLs")
            logger.info(f"🔢 selected urls: \n{chr(10).join(filtered_urls)}")

            return filtered_urls

        except Exception as e:
            logger.error(f"❌ error filtering URLs with LLM: {e}", exc_info=True)
            return urls  # return original URLs if filtering fails

    async def plan(self, state: WebsiteResearchState) -> dict:
        """Plan research by decomposing task into sub-tasks and matching URLs"""
        logger.info("📋 planning research sub-tasks")

        # create planning prompt
        planning_prompt = f"""你是专业的网站调研分析师，负责制定结构化的调研计划。

## 核心任务
1. 分析发现的URL页面，总结除调研子任务外的其他发现和洞察
2. 根据调研任务和网站页面，将调研工作分解为1-3个高效的子任务

## 调研信息
调研任务: 
```markdown
{state.research_task}
```

目标网站: {state.website_url}

## 发现的页面URL（共{len(state.discovered_urls)}个）
```plaintext
{chr(10).join(state.discovered_urls)} 
```


## 分析要求
1. **额外洞察发现**：基于URL结构分析，识别与调研任务相关但不需要深度分析的其他信息
2. **网站结构洞察**：从URL模式推断网站架构、功能模块、业务范围等
3. **补充信息识别**：发现可能对调研有帮助但不作为主要调研目标的信息点
4. **线索整合**：在发现描述中直接引用和分析相关的重要URL链接

## 分解原则
1. **任务聚焦**：每个子任务有明确的调研目标和范围
2. **页面归类**：同类型或相关主题的URL归入同一子任务
3. **价值优先**：优先选择信息价值高的URL页面
4. **资源高效**：每个URL只分配给一个子任务，避免重复

## 执行规则
- 子任务数量：1-5个
- 每个子任务包含1-5个相关URL
- task_id使用连续整数（1, 2, 3）
- 按页面类型和内容主题合理分组
- 每个子任务有具体的调研重点说明

## 输出要求
- 使用2个空格缩进，不要添加任何解释或说明
- url_insights：包含详细的发现描述和相关URL的分析
- 所有URL分析都要具体、详细，避免泛泛而谈
- 在洞察中直接引用具体的URL进行分析
"""

        # use structured output to get planning result
        structured_model = self.query_generation_model.with_structured_output(ResearchPlan)
        planning_result = await structured_model.ainvoke([HumanMessage(content=planning_prompt)])

        # convert pydantic models to dict format
        sub_tasks = planning_result.sub_tasks
        url_insights = planning_result.url_insights

        logger.info(f"📊 created {len(sub_tasks)} sub-tasks")
        logger.info(f"🎯 planning rationale: {planning_result.rationale}")

        # log the url insights
        logger.info(f"🔍 URL insights: {url_insights}")

        # log the sub-tasks
        for task in sub_tasks:
            logger.info(f"🎯 Sub-task {task.task_id}: {task.description}")
            logger.info(f"🔍 Explanation: {task.explanation}")
            logger.info(f"🔗 URLs: {task.urls}")

        return {"sub_tasks": sub_tasks, "url_insights": url_insights}

    async def web_research(self, state: ScrapeTaskState) -> dict:
        """Scrape and analyze pages for a specific sub-task"""
        task = state["search_task"]
        task_id = task.task_id
        description = task.description
        urls = task.urls
        # research_task = state.research_task

        logger.info(f"🔍 scraping task {task_id}: {description}")
        logger.info(f"📄 processing {len(urls)} URLs")

        # combine research_task and sub-task description for better context
        query = f"""
{task.description}
{task.explanation}
"""

        # use firecrawl to scrape and analyze pages
        result = await firecrawl_scrape.ainvoke(
            input={
                "explanation": "This tool is used to scrape the content of the page.",
                "query": query,
                "urls": urls,
            }
        )

        logger.info(f"✅ completed task {task_id}")
        return {"completed_tasks": {task_id: result}}

    async def reflection(self, state: WebsiteResearchState) -> dict:
        """Reflect on the gathered research results and determine if more information is needed"""
        logger.info("🤔 reflecting on research completeness")

        # increment research loop count
        current_loop = state.research_loop_count + 1
        max_loops = state.max_research_loops

        # check if we've reached max loops - if so, mark as sufficient without LLM call
        if current_loop >= max_loops:
            logger.info(f"🔄 reached max research loops ({max_loops}), proceeding to final report")
            return {
                "is_sufficient": True,
                "knowledge_gap": "",
                "research_loop_count": current_loop,
            }

        # prepare reflection prompt
        completed_results = []
        searched_urls = []
        for task_id, result in state.completed_tasks.items():
            # find task description
            task: SubTask | None = None
            for t in state.sub_tasks:
                if t.task_id == task_id:
                    task = t
                    searched_urls.extend(task.urls)
                    break
            completed_results.append(
                f"## 子任务 {task_id}: {task.description}\nURLs: {chr(10).join(task.urls)}\n{result}\n"
            )

        reflection_prompt = f"""你是一个专业的调研分析师，正在评估当前的调研结果是否足够回答原始调研任务。

## 原始调研任务
{state.research_task}

## 目标网站
{state.website_url}

## 当前调研结果
{("-----\n\n").join(completed_results)}

## 可检索的URL列表
{chr(10).join(set(state.discovered_urls) - set(searched_urls))}

## 评估要求
请仔细分析当前的调研结果，判断是否足够回答原始调研任务。如果信息不够充分，请设计具体的补充调研子任务。

## 输出格式
请按照以下JSON格式输出评估结果：

```json
{{
    "is_sufficient": true/false,
    "knowledge_gap": "描述缺失的信息或需要澄清的内容（如果信息充分则为空字符串）",
    "follow_up_tasks": [
        {{
            "task_id": {max(state.completed_tasks.keys()) + 1 if state.completed_tasks else 1},
            "explanation": "补充调研策略说明",
            "description": "具体的补充调研任务描述",
            "urls": ["相关URL1", "相关URL2"]
        }}
    ] // 如果信息充分则为空数组
}}
```

注意：
- 如果当前信息已经能够充分回答调研任务，设置 is_sufficient 为 true，follow_up_tasks 为空数组
- 如果需要更多信息，生成1-3个具体的补充调研子任务
- 每个子任务应该从已发现的URL池中选择1-5个最相关的URL
- task_id 应该从 {max(state.completed_tasks.keys()) + 1 if state.completed_tasks else 1} 开始递增编号
- 你只能从可检索的URL列表中选择 URL
- 不要重复分析相同的 URL
"""

        # use structured output to get reflection result
        structured_model = self.query_generation_model.with_structured_output(Reflection)
        reflection_result = await structured_model.ainvoke([HumanMessage(content=reflection_prompt)])

        logger.info(f"📊 reflection result - sufficient: {reflection_result.is_sufficient}")

        if not reflection_result.is_sufficient and reflection_result.follow_up_tasks:
            logger.info(f"🔍 knowledge gap: {reflection_result.knowledge_gap}")
            logger.info(f"❓ follow-up tasks: {len(reflection_result.follow_up_tasks)} additional tasks")

            # add new sub-tasks to existing ones
            new_sub_tasks = state.sub_tasks + reflection_result.follow_up_tasks

            return {
                "is_sufficient": False,
                "knowledge_gap": reflection_result.knowledge_gap,
                "research_loop_count": current_loop,
                "sub_tasks": new_sub_tasks,
            }
        else:
            return {
                "is_sufficient": True,
                "knowledge_gap": reflection_result.knowledge_gap,
                "research_loop_count": current_loop,
            }

    async def finalize_answer(self, state: WebsiteResearchState) -> dict:
        """Generate final research report"""
        logger.info("📝 generating final research report")

        # prepare report generation prompt
        completed_results = []
        for task_id, result in state.completed_tasks.items():
            # find task description
            task_desc = "未知任务"
            for task in state.sub_tasks:
                if task.task_id == task_id:
                    task_desc = task.description
                    break

            completed_results.append(f"## {task_desc}\n{result}\n")

        # prepare apollo organization details in yaml format
        apollo_section = ""
        if state.organization_details and "error" not in state.organization_details:
            apollo_yaml = yaml.dump(state.organization_details, indent=2, default_flow_style=False)
            apollo_section = f"""
## Apollo 机构详情
```yaml
{apollo_yaml}
```
"""

        report_prompt = f"""基于以下调研结果，合成一份专业完整的公司调研报告。
## 任务
- 将各子任务调研结果进行合成，生成一份结构清晰、内容详实的最终调研报告
- 包含所有子任务的调研结果，回答原始调研任务
- 你的回答来源的信息只能来源于当前上下文

## 原始调研任务
{state.research_task}

## 调研网站
{state.website_url}

## URL洞察分析
{state.url_insights}
{apollo_section}
## 各子任务调研结果:
{("-----\n\n").join(completed_results)}


## 输出要求
- Highlight key findings and insights
- Keep the links in the content for further research
- Always provide a citation for the source
- Include proper citations for all sources
- Use Reference Links citations for everything using markdown link notation with the url provided
- Define Reference Links at the end of the document
- All facts must be cited

在保持学术严谨性和事实准确性的前提下，补充更多信息、更深入的见解，以提升本研究报告的质量
请确保报告专业、准确、有条理
直接输出报告内容，无需其他解释"""

        # generate final report
        report_result = await self.finalize_model.ainvoke([HumanMessage(content=report_prompt)])
        final_report = report_result.text()

        logger.info("📊 final report generated successfully")

        return {"final_report": final_report}

    def continue_to_web_search(self, state: WebsiteResearchState) -> list[Send]:
        """Determine which sub-tasks to send for scraping"""
        return [
            Send("web_research", {"search_task": sub_task, "research_task": state.research_task})
            for sub_task in state.sub_tasks
        ]

    def evaluate_research(self, state: WebsiteResearchState) -> str | list[Send]:
        """Conditional edge to evaluate research completeness and decide next step"""
        logger.info("🔄 evaluating research completeness")

        if state.is_sufficient:
            logger.info("✅ research is sufficient, generating final report")
            return "finalize_answer"
        else:
            # get only the new sub-tasks (those not already completed)
            completed_task_ids = set(state.completed_tasks.keys())
            new_sub_tasks = [task for task in state.sub_tasks if task.task_id not in completed_task_ids]

            if new_sub_tasks:
                logger.info(f"🔍 research insufficient, sending {len(new_sub_tasks)} additional sub-tasks for scraping")
                return [
                    Send("web_research", {"search_task": sub_task, "research_task": state.research_task})
                    for sub_task in new_sub_tasks
                ]
            else:
                logger.info("🔄 no additional sub-tasks generated, proceeding to final report")
                return "finalize_answer"

    def build_graph(self) -> CompiledStateGraph:
        """Build and compile the website research workflow"""

        # create workflow
        workflow = StateGraph(WebsiteResearchState)

        # add nodes
        workflow.add_node("website_map", self.website_map)
        workflow.add_node("apollo_research", self.apollo_research)
        workflow.add_node("plan", self.plan)
        workflow.add_node("web_research", self.web_research)
        workflow.add_node("reflection", self.reflection)
        workflow.add_node("finalize_answer", self.finalize_answer)

        # set entry point
        workflow.set_entry_point("website_map")
        workflow.set_entry_point("apollo_research")

        # define workflow edges
        workflow.add_edge(["website_map", "apollo_research"], end_key="plan")
        workflow.add_conditional_edges("plan", self.continue_to_web_search, path_map=["web_research"])
        workflow.add_edge("web_research", "reflection")
        workflow.add_conditional_edges(
            "reflection", self.evaluate_research, path_map=["web_research", "finalize_answer"]
        )
        workflow.add_edge("finalize_answer", END)

        # compile the workflow
        return workflow.compile(name="website_research_workflow")

    async def research_website(self, research_task: str, website_url: str) -> str:
        """
        Execute website research for a specific task and URL

        Args:
            research_task: the research task description
            website_url: company official website URL

        Returns:
            stream of events from the graph
        """
        logger.info(f"🚀 starting website research for {website_url}")
        logger.info(f"📋 task: {research_task}")

        # build the graph
        graph = self.build_graph()

        # create initial state
        initial_state = WebsiteResearchState(
            research_task=research_task,
            website_url=website_url,
        )

        # stream the execution
        from langfuse.langchain import CallbackHandler

        from utils.logging_callback_handler import LoggingCallbackHandler

        # print(graph.get_graph().draw_mermaid())
        # return
        state = await graph.ainvoke(initial_state, config={"callbacks": [CallbackHandler(), LoggingCallbackHandler()]})
        return state["final_report"]
