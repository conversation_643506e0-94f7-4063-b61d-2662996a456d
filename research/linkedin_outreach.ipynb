{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langfuse.callback import CallbackHandler\n", "\n", "langfuse_handler = CallbackHandler(\n", "    public_key=\"pk-lf-3072d09c-c138-421a-bd2c-4322c8e11da4\",\n", "    secret_key=\"sk-lf-d9cc38bb-c25d-4a51-baa6-24bbb422872c\",\n", "    host=\"https://us.cloud.langfuse.com\",\n", ")\n", "\n", "# <Your Langchain code here>\n", "\n", "# Add handler to run/invoke/call/chat\n", "# chain.invoke({\"input\": \"<user_input>\"}, config={\"callbacks\": [langfuse_handler]})"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "llm: ChatOpenAI = ChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "# llm = ChatOpenAI(model=\"o3-mini\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def load_file(file_path) -> str:\n", "    with open(file_path, \"r\") as f:\n", "        return f.read()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import pandas as pd\n", "\n", "cases = [\n", "    {\n", "        \"company\": \"ManuAuto\",\n", "        \"keywords\": \"加拿大工业物联网产品(MOXA)的代理商\",\n", "        \"expected_customer\": \"Mo Salehian\",\n", "    },\n", "    {\n", "        \"company\": \"AFD Petroleum Ltd\",\n", "        \"keywords\": \"加拿大储油设备 IoT 远程监控方案\",\n", "        \"expected_customer\": \"<PERSON>\",\n", "    },\n", "    {\n", "        \"company\": \"OPHARDT Hygiene\",\n", "        \"keywords\": \"加拿大卫生间智能设备联网需求\",\n", "        \"expected_customer\": \"<PERSON>\",\n", "    },\n", "    {\n", "        \"company\": \"Curb (Home Energy Management)\",\n", "        \"keywords\": \"美国家庭用电配电盒cellular联网需求\",\n", "        \"expected_customer\": \"<PERSON>\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import JsonOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "\n", "def convert_people_list_to_csv(people_list: list[dict]) -> str:\n", "    df = pd.DataFrame(people_list)\n", "    # delete url column it's useless for people evaluation\n", "    if \"url\" in df.columns:\n", "        df = df.drop(columns=[\"url\"])\n", "    return df.to_csv(index=False)\n", "\n", "\n", "def evaluate_people_list(people_list: list[dict], keywords: str) -> dict:\n", "    \"\"\"\n", "    评估人员列表\n", "    \"\"\"\n", "\n", "    # 为了在测试的时候，每次调用都可以立即生成新的 Prompt 模板，所以每次调用都会重新加载 Prompt 模板\n", "    system_prompt = load_file(\"./research/user_subtitle_outreach_prompt.md\")\n", "\n", "    prompts = ChatPromptTemplate.from_messages(\n", "        [\n", "            (\"system\", system_prompt),\n", "            (\n", "                \"user\",\n", "                \"请根据以下人员列表找出最适合作为[{keywords}]的潜在联系人的人员。联系人名单：\\n{people_list}\",\n", "            ),\n", "        ]\n", "    )\n", "    chain = prompts | llm | JsonOutputParser()\n", "\n", "    result = chain.invoke(\n", "        {\"people_list\": convert_people_list_to_csv(people_list), \"keywords\": keywords},\n", "        config={\"callbacks\": [langfuse_handler]},\n", "    )\n", "    # return result if isinstance(result, list) else [result]\n", "    return result"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["预期客户:  <PERSON>\n", "从83个候选人中筛选出5个候选人\n", "思考过程:  我现在正在寻找推销加拿大储油设备 IoT 远程监控方案的潜在联系人。根据思考策略，这个解决方案的解决痛点是提高储油设备的监控效率和安全性，对应的目标行业和客户画像是石油行业的设备管理和技术部门。因此我需要寻找与产品开发、IT管理、系统集成、项目管理等相关的潜在联系人。我将重点关注职位关键词包含“产品开发”、“IT管理”、“系统集成”、“项目管理”的联系人。\n", "推荐客户:  <PERSON>\n", "推荐理由:  <PERSON>作为产品开发经理，负责新产品的开发和技术集成，可能对引入新的IoT监控方案有直接的兴趣和决策影响力。\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>position</th>\n", "      <th>thinking</th>\n", "      <th>priority</th>\n", "      <th>match_reason</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>Product Development Manager</td>\n", "      <td><PERSON>作为产品开发经理，负责新产品的开发和技术集成，可能对引入新的Io...</td>\n", "      <td>5</td>\n", "      <td>[产品开发, 技术集成]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON></td>\n", "      <td>IT Manager | Power Platform Developer | Cybers...</td>\n", "      <td><PERSON>作为IT经理，负责公司内部的技术管理和安全，可能对IoT解决方案的技术...</td>\n", "      <td>5</td>\n", "      <td>[IT管理, 技术实施]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Product Development Lead @ AFD Petroleum Ltd. ...</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Boparai作为产品开发负责人，专注于行业标准和问题解决，可能对IoT监控方...</td>\n", "      <td>4</td>\n", "      <td>[产品开发, 行业标准]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON></td>\n", "      <td>Senior Business Systems Analyst</td>\n", "      <td><PERSON>作为高级业务系统分析师，可能负责评估和优化业务系统，IoT监控方案可能...</td>\n", "      <td>4</td>\n", "      <td>[业务系统, 技术改进]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON></td>\n", "      <td>Project Manager, Organizational Improvement, L...</td>\n", "      <td><PERSON>作为项目经理，负责组织改进和产品支持，可能对IoT监控方案的项目...</td>\n", "      <td>4</td>\n", "      <td>[项目管理, 组织改进]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             name                                           position  \\\n", "0  <PERSON>                        Product Development Manager   \n", "1     <PERSON>  IT Manager | Power Platform Developer | Cybers...   \n", "2  Gurbaj Boparai  Product Development Lead @ AFD Petroleum Ltd. ...   \n", "3     <PERSON>                    Senior Business Systems Analyst   \n", "4  <PERSON>  Project Manager, Organizational Improvement, L...   \n", "\n", "                                            thinking  priority  match_reason  \n", "0  Jeremiah Burak作为产品开发经理，负责新产品的开发和技术集成，可能对引入新的Io...         5  [产品开发, 技术集成]  \n", "1  <PERSON>作为IT经理，负责公司内部的技术管理和安全，可能对IoT解决方案的技术...         5  [IT管理, 技术实施]  \n", "2  Gurbaj Boparai作为产品开发负责人，专注于行业标准和问题解决，可能对IoT监控方...         4  [产品开发, 行业标准]  \n", "3  <PERSON>作为高级业务系统分析师，可能负责评估和优化业务系统，IoT监控方案可能...         4  [业务系统, 技术改进]  \n", "4  <PERSON>作为项目经理，负责组织改进和产品支持，可能对IoT监控方案的项目...         4  [项目管理, 组织改进]  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["case = cases[1]\n", "keywords = case[\"keywords\"]\n", "company = case[\"company\"]\n", "\n", "with open(f\"data/{company}/original_people_list.json\", \"r\") as f:\n", "    peoples = json.load(f)\n", "\n", "pd.<PERSON><PERSON><PERSON><PERSON>(peoples)\n", "\n", "result = evaluate_people_list(peoples, keywords)\n", "if \"thinking\" not in result:\n", "    raise ValueError(\"thinking not in result\")\n", "\n", "print(\"预期客户: \", case[\"expected_customer\"])\n", "print(\"从{}个候选人中筛选出{}个候选人\".format(len(peoples), len(result[\"contacts\"])))\n", "print(\"思考过程: \", result[\"thinking\"])\n", "print(\"推荐客户: \", result[\"contacts\"][0][\"name\"])\n", "print(\"推荐理由: \", result[\"contacts\"][0][\"thinking\"])\n", "# Convert the dictionary to a pandas DataFrame\n", "pd.DataFrame(result[\"contacts\"])"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["预期客户:  <PERSON>\n", "从5个候选人中筛选出3个候选人\n", "思考过程:  我现在正在寻找推销加拿大工业物联网产品(MOXA)的代理商的潜在联系人。根据思考策略，这个解决方案的解决痛点是提高生产效率、降低成本、改善安全管理等，对应的目标行业和客户画像是工业自动化和物联网领域的企业。因此我需要寻找在这些领域有决策权或影响力的潜在联系人。我将重点关注职位关键词包含'销售'、'业务发展'、'运营'、'自动化'、'IIOT'的联系人。\n", "推荐客户:  <PERSON>\n", "推荐理由:  <PERSON>作为运营总监，负责销售和企业发展，具备采购决策权和影响力，适合作为潜在联系人。\n", "预期客户:  <PERSON>\n", "从83个候选人中筛选出5个候选人\n", "思考过程:  我现在正在寻找推销加拿大储油设备 IoT 远程监控方案的潜在联系人。根据思考策略，这个解决方案的解决痛点是提高储油设备的监控效率和安全性，对应的目标行业和客户画像是石油行业的设备管理和技术部门。因此我需要寻找与产品开发、IT管理、项目管理相关的潜在联系人。我将重点关注职位关键词包含'Product Development'、'IT Manager'、'Project Manager'的联系人。\n", "推荐客户:  <PERSON>\n", "推荐理由:  <PERSON>作为产品开发经理，负责新产品的开发和技术选型，可能对引入新的IoT解决方案有直接的兴趣和决策影响力。\n", "预期客户:  <PERSON>\n", "从16个候选人中筛选出5个候选人\n", "思考过程:  我现在正在寻找推销加拿大卫生间智能设备联网需求的潜在联系人。根据思考策略，这个解决方案的解决痛点是提高卫生间设备的智能化和联网能力，对应的目标行业和客户画像是卫生设备制造商和相关技术集成商。因此我需要寻找与产品管理、技术集成、销售和市场相关的潜在联系人。我重点关注职位关键词包含'产品管理'、'技术'、'销售'、'市场'的联系人。\n", "推荐客户:  <PERSON>\n", "推荐理由:  <PERSON>负责全球产品类别，可能对产品创新和技术集成有决策权或影响力，适合作为潜在联系人。\n", "预期客户:  <PERSON>\n", "从4个候选人中筛选出3个候选人\n", "思考过程:  我现在正在寻找推销美国家庭用电配电盒cellular联网需求的潜在联系人，根据思考策略，这个解决方案的解决痛点是提高家庭用电管理的效率和安全性，对应的目标行业和客户画像是家庭电力设备制造商和相关技术集成商，因此我需要寻找与电力设备、物联网技术相关的潜在联系人。我就当重点关注职位关键词包含“解决方案”、“系统集成”、“项目管理”、“IT 管理”等的联系人。\n", "推荐客户:  <PERSON>\n", "推荐理由:  <PERSON>tterjee作为解决方案工程师，涉及多个行业，包括电子和半导体，这与家庭用电配电盒的技术需求相关。他的职位表明他可能参与技术选型和系统集成，适合作为潜在联系人。\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>position</th>\n", "      <th>thinking</th>\n", "      <th>company</th>\n", "      <th>url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>Director of Operations @ Manufacturers Automat...</td>\n", "      <td><PERSON>作为运营总监，负责销售和企业发展，具备采购决策权和影响力，适合作为潜在...</td>\n", "      <td>ManuAuto</td>\n", "      <td>https://www.linkedin.com/in/mo-salehian-3b2033...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Area Sales Manager at Manufacturers Automation...</td>\n", "      <td>Us<PERSON>f作为区域销售经理，专注于业务发展、工厂自动化和IIOT，直接与我们的...</td>\n", "      <td>ManuAuto</td>\n", "      <td>https://www.linkedin.com/in/usman-ashraf-ab02b...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>Network Application Engineer</td>\n", "      <td><PERSON>作为网络应用工程师，虽然职位不在高层，但在技术选型和实施中...</td>\n", "      <td>ManuAuto</td>\n", "      <td>https://www.linkedin.com/in/kenny-dela-piedra-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON></td>\n", "      <td>Product Development Manager</td>\n", "      <td><PERSON>作为产品开发经理，负责新产品的开发和技术选型，可能对引入新的Io...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/jeremia<PERSON>-burak-936...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON></td>\n", "      <td>IT Manager | Power Platform Developer | Cybers...</td>\n", "      <td><PERSON>作为IT经理，负责公司内部的技术管理和安全，可能对IoT远程监控方案的...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/jldale?miniProfile...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Product Development Lead @ AFD Petroleum Ltd. ...</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>ai在产品开发方面有丰富经验，并且关注行业标准和问题解决，可能对Io...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/gurbaj-boparai-722...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON></td>\n", "      <td>Project Manager, Organizational Improvement, L...</td>\n", "      <td><PERSON>作为项目经理，负责组织改进和产品支持，可能对IoT方案在项目中的...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/richard-loewen-793...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>Senior Business Systems Analyst</td>\n", "      <td><PERSON>作为高级业务系统分析师，可能负责评估和优化业务系统，对IoT解决方案的...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/alex-martin-453525...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td><PERSON></td>\n", "      <td>Global Product Categories- Ophardt Hygiene- Te...</td>\n", "      <td><PERSON>负责全球产品类别，可能对产品创新和技术集成有决策权或影响力，适合...</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/klaus-z<PERSON>pe-094...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td><PERSON></td>\n", "      <td>Business Unit Director at OPHARDT Hygiene</td>\n", "      <td><PERSON>作为业务单元总监，可能负责整体业务战略和产品引进，具备较高的决策权，适...</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/fredroffel?miniPro...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td><PERSON></td>\n", "      <td>Sales Manager BeNeLux / Internal Sales Manager...</td>\n", "      <td><PERSON>负责销售管理，可能对新产品的市场推广和销售策略有影响力，适合作为...</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/manfred-weenen-2bb...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td><PERSON></td>\n", "      <td>Engineering Manager, Product Manager - <PERSON><PERSON><PERSON>...</td>\n", "      <td>Ted House作为工程和产品经理，可能负责产品开发和技术集成，适合作为潜在联系人。</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/ted-house-8546282?...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td><PERSON></td>\n", "      <td>Technical Operations Manager at OPHARDT Hygiene</td>\n", "      <td><PERSON>负责技术运营，可能对技术实施和设备联网有实际需求和影响力，适合作为...</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/joshua-geurts-3939...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td><PERSON></td>\n", "      <td>Solutions Engineer</td>\n", "      <td><PERSON>作为解决方案工程师，涉及多个行业，包括电子和半导体，这与家庭用...</td>\n", "      <td><PERSON><PERSON><PERSON> (Home Energy Management)</td>\n", "      <td>https://www.linkedin.com/in/billchatterjee?min...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Operations Mgr-Curb</td>\n", "      <td><PERSON><PERSON>作为运营经理，可能负责相关项目的运营和管理，具备一定的决策影响力，适...</td>\n", "      <td><PERSON><PERSON><PERSON> (Home Energy Management)</td>\n", "      <td>https://www.linkedin.com/in/terri-morfin-a8817...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td><PERSON></td>\n", "      <td>Real Time Information and Transaction Specialist</td>\n", "      <td><PERSON> <PERSON>的职位涉及实时信息和交易，可能与物联网技术和数据管理相关，具备一定的技术背景...</td>\n", "      <td><PERSON><PERSON><PERSON> (Home Energy Management)</td>\n", "      <td>https://www.linkedin.com/in/tim-ryan-212b9?min...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 name                                           position  \\\n", "0         Mo Salehian  Director of Operations @ Manufacturers Automat...   \n", "1        Usman <PERSON>  Area Sales Manager at Manufacturers Automation...   \n", "2   <PERSON>a Piedra                       Network Application Engineer   \n", "3      <PERSON>                        Product Development Manager   \n", "4         <PERSON>  IT Manager | Power Platform Developer | Cybers...   \n", "5      Gurbaj Boparai  Product Development Lead @ AFD Petroleum Ltd. ...   \n", "6      <PERSON>  Project Manager, Organizational Improvement, L...   \n", "7         <PERSON>                    Senior Business Systems Analyst   \n", "8      Klaus <PERSON>pe  Global Product Categories- Ophardt Hygiene- Te...   \n", "9         <PERSON>          Business Unit Director at OPHARDT Hygiene   \n", "10     <PERSON>  Sales Manager <PERSON><PERSON>e<PERSON>ux / Internal Sales Manager...   \n", "11          <PERSON>  Engineering Manager, Product Manager - <PERSON><PERSON><PERSON>...   \n", "12      <PERSON>    Technical Operations Manager at OPHARDT Hygiene   \n", "13    <PERSON>                                 Solutions Engineer   \n", "14       Terri <PERSON>                                Operations Mgr-Curb   \n", "15           <PERSON>   Real Time Information and Transaction Specialist   \n", "\n", "                                             thinking  \\\n", "0   Mo Salehian作为运营总监，负责销售和企业发展，具备采购决策权和影响力，适合作为潜在...   \n", "1   Usman Ashraf作为区域销售经理，专注于业务发展、工厂自动化和IIOT，直接与我们的...   \n", "2   <PERSON>作为网络应用工程师，虽然职位不在高层，但在技术选型和实施中...   \n", "3   <PERSON> Burak作为产品开发经理，负责新产品的开发和技术选型，可能对引入新的Io...   \n", "4   <PERSON>作为IT经理，负责公司内部的技术管理和安全，可能对IoT远程监控方案的...   \n", "5   Gurbaj Boparai在产品开发方面有丰富经验，并且关注行业标准和问题解决，可能对Io...   \n", "6   <PERSON>作为项目经理，负责组织改进和产品支持，可能对IoT方案在项目中的...   \n", "7   <PERSON> Martin作为高级业务系统分析师，可能负责评估和优化业务系统，对IoT解决方案的...   \n", "8   <PERSON> Zscher<PERSON>负责全球产品类别，可能对产品创新和技术集成有决策权或影响力，适合...   \n", "9   <PERSON> Roffel作为业务单元总监，可能负责整体业务战略和产品引进，具备较高的决策权，适...   \n", "10  <PERSON>n负责销售管理，可能对新产品的市场推广和销售策略有影响力，适合作为...   \n", "11        Ted House作为工程和产品经理，可能负责产品开发和技术集成，适合作为潜在联系人。   \n", "12  <PERSON> Geurts负责技术运营，可能对技术实施和设备联网有实际需求和影响力，适合作为...   \n", "13  <PERSON>tterjee作为解决方案工程师，涉及多个行业，包括电子和半导体，这与家庭用...   \n", "14  Te<PERSON> Morfin作为运营经理，可能负责相关项目的运营和管理，具备一定的决策影响力，适...   \n", "15  <PERSON>的职位涉及实时信息和交易，可能与物联网技术和数据管理相关，具备一定的技术背景...   \n", "\n", "                          company  \\\n", "0                        ManuAuto   \n", "1                        ManuAuto   \n", "2                        ManuAuto   \n", "3               AFD Petroleum Ltd   \n", "4               AFD Petroleum Ltd   \n", "5               AFD Petroleum Ltd   \n", "6               AFD Petroleum Ltd   \n", "7               AFD Petroleum Ltd   \n", "8                 OPHARDT Hygiene   \n", "9                 OPHARDT Hygiene   \n", "10                OPHARDT Hygiene   \n", "11                OPHARDT Hygiene   \n", "12                OPHARDT Hygiene   \n", "13  Curb (Home Energy Management)   \n", "14  Curb (Home Energy Management)   \n", "15  Curb (Home Energy Management)   \n", "\n", "                                                  url  \n", "0   https://www.linkedin.com/in/mo-salehian-3b2033...  \n", "1   https://www.linkedin.com/in/usman-ashraf-ab02b...  \n", "2   https://www.linkedin.com/in/kenny-dela-piedra-...  \n", "3   https://www.linkedin.com/in/jeremia<PERSON>-burak-936...  \n", "4   https://www.linkedin.com/in/jldale?miniProfile...  \n", "5   https://www.linkedin.com/in/gurbaj-boparai-722...  \n", "6   https://www.linkedin.com/in/richard-loewen-793...  \n", "7   https://www.linkedin.com/in/alex-martin-453525...  \n", "8   https://www.linkedin.com/in/klaus-z<PERSON><PERSON>-094...  \n", "9   https://www.linkedin.com/in/fredroffel?miniPro...  \n", "10  https://www.linkedin.com/in/manfred-weenen-2bb...  \n", "11  https://www.linkedin.com/in/ted-house-8546282?...  \n", "12  https://www.linkedin.com/in/joshua-geurts-3939...  \n", "13  https://www.linkedin.com/in/billchatterjee?min...  \n", "14  https://www.linkedin.com/in/terri-morfin-a8817...  \n", "15  https://www.linkedin.com/in/tim-ryan-212b9?min...  "]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame()\n", "\n", "for case in cases:\n", "    keywords = case[\"keywords\"]\n", "    company = case[\"company\"]\n", "\n", "    with open(f\"data/{company}/original_people_list.json\", \"r\") as f:\n", "        peoples = json.load(f)\n", "\n", "    result = evaluate_people_list(peoples, keywords)\n", "    if \"thinking\" not in result:\n", "        raise ValueError(\"thinking not in result\")\n", "\n", "    expected_customer = case[\"expected_customer\"]\n", "    print(\"预期客户: \", expected_customer)\n", "    print(\"从{}个候选人中筛选出{}个候选人\".format(len(peoples), len(result[\"contacts\"])))\n", "    print(\"思考过程: \", result[\"thinking\"])\n", "    print(\"推荐客户: \", result[\"contacts\"][0][\"name\"])\n", "    print(\"推荐理由: \", result[\"contacts\"][0][\"thinking\"])\n", "\n", "    # 检查预期候选人是否在推荐列表中\n", "    recommended_names = [contact[\"name\"] for contact in result[\"contacts\"]]\n", "    if expected_customer and expected_customer not in recommended_names:\n", "        print(f\"\\033[91m警告: 预期候选人 '{expected_customer}' 不在推荐列表中!\\033[0m\")\n", "        print(f\"\\033[91m推荐列表: {recommended_names}\\033[0m\")\n", "    result_df = pd.DataFrame(result[\"contacts\"])\n", "    result_df[\"company\"] = company\n", "    # match url from original people list\n", "    for i, contact in enumerate(result_df.itertuples()):\n", "        for person in peoples:\n", "            if person.get(\"name\") == contact.name:\n", "                result_df.at[i, \"url\"] = person.get(\"url\", \"\")\n", "                break\n", "    df = pd.concat([df, result_df], ignore_index=True)\n", "\n", "df"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["# save filtered people list as json file\n", "df.to_json(\"research/filtered_people_list.json\", orient=\"records\", force_ascii=False, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Filtered people list with about description\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>position</th>\n", "      <th>thinking</th>\n", "      <th>company</th>\n", "      <th>url</th>\n", "      <th>about</th>\n", "      <th>position_description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>Director of Operations @ Manufacturers Automat...</td>\n", "      <td><PERSON>作为运营总监，负责销售和企业发展，具备采购决策影响力，并且与工业自动化...</td>\n", "      <td>ManuAuto</td>\n", "      <td>https://www.linkedin.com/in/mo-salehian-3b2033...</td>\n", "      <td>With over 20 years in Communication and indust...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Area Sales Manager at Manufacturers Automation...</td>\n", "      <td><PERSON><PERSON>f是区域销售经理，专注于业务发展、工厂自动化、工业4.0和IIOT，直...</td>\n", "      <td>ManuAuto</td>\n", "      <td>https://www.linkedin.com/in/usman-ashraf-ab02b...</td>\n", "      <td>As a dedicated professional with a strong back...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>Network Application Engineer</td>\n", "      <td><PERSON>作为网络应用工程师，可能参与技术选型和实施，虽然职位不高，...</td>\n", "      <td>ManuAuto</td>\n", "      <td>https://www.linkedin.com/in/kenny-dela-piedra-...</td>\n", "      <td>An IT/OT Engineer with a proven track record o...</td>\n", "      <td>Manufacturers Automation Inc. Is a value added...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON></td>\n", "      <td>Product Development Manager</td>\n", "      <td><PERSON>作为产品开发经理，负责新产品的开发和技术选型，与我们的IoT解决...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/jeremia<PERSON>-burak-936...</td>\n", "      <td>Passionate about merging the worlds of technol...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON></td>\n", "      <td>IT Manager | Power Platform Developer | Cybers...</td>\n", "      <td><PERSON>作为IT经理，负责公司内部的技术管理和安全，可能对IoT解决方案的技术...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/jldale?miniProfile...</td>\n", "      <td>Experienced IT Manager with over 15 years of e...</td>\n", "      <td>- Led the transition from a Managed Service Pr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Product Development Lead @ AFD Petroleum Ltd. ...</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Boparai作为产品开发负责人，专注于行业标准和问题解决，与我们的IoT解决...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/gurbaj-boparai-722...</td>\n", "      <td>As a Product Development Lead at AFD Petroleum...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON></td>\n", "      <td>Project Manager, Organizational Improvement, L...</td>\n", "      <td><PERSON>作为项目经理，负责组织改进和产品支持，可能对IoT解决方案的实施...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/richard-loewen-793...</td>\n", "      <td>As a results-driven Senior Operations Executiv...</td>\n", "      <td>Tasked with starting up the US-based operation...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>Senior Business Systems Analyst</td>\n", "      <td><PERSON>作为高级业务系统分析师，负责分析和优化业务系统，与IoT解决方案的集成...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/alex-martin-453525...</td>\n", "      <td>NaN</td>\n", "      <td>- ERP Change Management Microsoft Business Dyn...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td><PERSON></td>\n", "      <td>Global Product Categories- Ophardt Hygiene- Te...</td>\n", "      <td><PERSON>负责全球产品类别，可能对产品创新和技术集成有直接影响力，适合作为...</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/klaus-z<PERSON>pe-094...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td><PERSON></td>\n", "      <td>Business Unit Director at OPHARDT Hygiene</td>\n", "      <td><PERSON>作为业务单元总监，可能负责整体业务战略和产品线决策，具备较高的决策权和...</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/fredroffel?miniPro...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td><PERSON></td>\n", "      <td>Technical Operations Manager at OPHARDT Hygiene</td>\n", "      <td><PERSON>负责技术运营，可能对技术集成和设备联网有实际需求和影响力，适合作为...</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/joshua-geurts-3939...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td><PERSON></td>\n", "      <td>Engineering Manager, Product Manager - <PERSON><PERSON><PERSON>...</td>\n", "      <td>Ted House作为工程和产品经理，直接负责产品的技术和管理，可能对智能设备联网有实际需求。</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/ted-house-8546282?...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td><PERSON></td>\n", "      <td>Sales Manager BeNeLux / Internal Sales Manager...</td>\n", "      <td><PERSON>负责销售管理，可能对市场需求和产品推广有深入了解，能够影响采购决策。</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/manfred-weenen-2bb...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td><PERSON></td>\n", "      <td>Solutions Engineer🔃Actionable Results ➔ Operat...</td>\n", "      <td><PERSON>作为解决方案工程师，涉及多个行业的运营和研发，可能对物联网技术...</td>\n", "      <td><PERSON><PERSON><PERSON> (Home Energy Management)</td>\n", "      <td>https://www.linkedin.com/in/billchatterjee?min...</td>\n", "      <td>\"Effective Leadership is putting first-things-...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Operations Mgr-Curb</td>\n", "      <td><PERSON><PERSON>作为运营经理，可能负责相关项目的运营和管理，具备一定的决策影响力，适...</td>\n", "      <td><PERSON><PERSON><PERSON> (Home Energy Management)</td>\n", "      <td>https://www.linkedin.com/in/terri-morfin-a8817...</td>\n", "      <td>After working 20 years in distribution of elec...</td>\n", "      <td>*Purchasing of electronic components. *Demand ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td><PERSON></td>\n", "      <td>Real Time Information and Transaction Specialist</td>\n", "      <td><PERSON> <PERSON>的职位涉及实时信息和交易，可能与电力管理和物联网技术相关，适合作为潜在联系人。</td>\n", "      <td><PERSON><PERSON><PERSON> (Home Energy Management)</td>\n", "      <td>https://www.linkedin.com/in/tim-ryan-212b9?min...</td>\n", "      <td>Doing my honours year in Agricultural Science ...</td>\n", "      <td>Passionate subject matter expert on real time ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 name                                           position  \\\n", "0         Mo Salehian  Director of Operations @ Manufacturers Automat...   \n", "1        Usman <PERSON>  Area Sales Manager at Manufacturers Automation...   \n", "2   <PERSON>a Piedra                       Network Application Engineer   \n", "3      <PERSON>                        Product Development Manager   \n", "4         <PERSON>  IT Manager | Power Platform Developer | Cybers...   \n", "5      Gurbaj Boparai  Product Development Lead @ AFD Petroleum Ltd. ...   \n", "6      <PERSON>  Project Manager, Organizational Improvement, L...   \n", "7         <PERSON>                    Senior Business Systems Analyst   \n", "8      Klaus <PERSON>pe  Global Product Categories- Ophardt Hygiene- Te...   \n", "9         <PERSON>          Business Unit Director at OPHARDT Hygiene   \n", "10      <PERSON>    Technical Operations Manager at OPHARDT Hygiene   \n", "11          <PERSON>  Engineering Manager, Product Manager - <PERSON><PERSON><PERSON>...   \n", "12     <PERSON>  Sales Manager <PERSON><PERSON>e<PERSON>ux / Internal Sales Manager...   \n", "13    <PERSON>  Solutions Engineer🔃Actionable Results ➔ Operat...   \n", "14       Terri <PERSON>                                Operations Mgr-Curb   \n", "15           <PERSON>   Real Time Information and Transaction Specialist   \n", "\n", "                                             thinking  \\\n", "0   Mo Salehian作为运营总监，负责销售和企业发展，具备采购决策影响力，并且与工业自动化...   \n", "1   Usman Ashraf是区域销售经理，专注于业务发展、工厂自动化、工业4.0和IIOT，直...   \n", "2   <PERSON>作为网络应用工程师，可能参与技术选型和实施，虽然职位不高，...   \n", "3   Jeremiah Burak作为产品开发经理，负责新产品的开发和技术选型，与我们的IoT解决...   \n", "4   <PERSON>作为IT经理，负责公司内部的技术管理和安全，可能对IoT解决方案的技术...   \n", "5   Gurbaj Boparai作为产品开发负责人，专注于行业标准和问题解决，与我们的IoT解决...   \n", "6   <PERSON>作为项目经理，负责组织改进和产品支持，可能对IoT解决方案的实施...   \n", "7   <PERSON> Martin作为高级业务系统分析师，负责分析和优化业务系统，与IoT解决方案的集成...   \n", "8   <PERSON> Zscher<PERSON>负责全球产品类别，可能对产品创新和技术集成有直接影响力，适合作为...   \n", "9   <PERSON> Roffel作为业务单元总监，可能负责整体业务战略和产品线决策，具备较高的决策权和...   \n", "10  <PERSON> Geurts负责技术运营，可能对技术集成和设备联网有实际需求和影响力，适合作为...   \n", "11    Ted House作为工程和产品经理，直接负责产品的技术和管理，可能对智能设备联网有实际需求。   \n", "12   <PERSON> Weenen负责销售管理，可能对市场需求和产品推广有深入了解，能够影响采购决策。   \n", "13  <PERSON> Chatterjee作为解决方案工程师，涉及多个行业的运营和研发，可能对物联网技术...   \n", "14  Te<PERSON> Morfin作为运营经理，可能负责相关项目的运营和管理，具备一定的决策影响力，适...   \n", "15    <PERSON>的职位涉及实时信息和交易，可能与电力管理和物联网技术相关，适合作为潜在联系人。   \n", "\n", "                          company  \\\n", "0                        ManuAuto   \n", "1                        ManuAuto   \n", "2                        ManuAuto   \n", "3               AFD Petroleum Ltd   \n", "4               AFD Petroleum Ltd   \n", "5               AFD Petroleum Ltd   \n", "6               AFD Petroleum Ltd   \n", "7               AFD Petroleum Ltd   \n", "8                 OPHARDT Hygiene   \n", "9                 OPHARDT Hygiene   \n", "10                OPHARDT Hygiene   \n", "11                OPHARDT Hygiene   \n", "12                OPHARDT Hygiene   \n", "13  Curb (Home Energy Management)   \n", "14  Curb (Home Energy Management)   \n", "15  Curb (Home Energy Management)   \n", "\n", "                                                  url  \\\n", "0   https://www.linkedin.com/in/mo-salehian-3b2033...   \n", "1   https://www.linkedin.com/in/usman-ashraf-ab02b...   \n", "2   https://www.linkedin.com/in/kenny-dela-piedra-...   \n", "3   https://www.linkedin.com/in/jeremia<PERSON>-burak-936...   \n", "4   https://www.linkedin.com/in/jldale?miniProfile...   \n", "5   https://www.linkedin.com/in/gurbaj-boparai-722...   \n", "6   https://www.linkedin.com/in/richard-loewen-793...   \n", "7   https://www.linkedin.com/in/alex-martin-453525...   \n", "8   https://www.linkedin.com/in/klaus-z<PERSON><PERSON>-094...   \n", "9   https://www.linkedin.com/in/fredroffel?miniPro...   \n", "10  https://www.linkedin.com/in/joshua-geurts-3939...   \n", "11  https://www.linkedin.com/in/ted-house-8546282?...   \n", "12  https://www.linkedin.com/in/manfred-weenen-2bb...   \n", "13  https://www.linkedin.com/in/billchatterjee?min...   \n", "14  https://www.linkedin.com/in/terri-morfin-a8817...   \n", "15  https://www.linkedin.com/in/tim-ryan-212b9?min...   \n", "\n", "                                                about  \\\n", "0   With over 20 years in Communication and indust...   \n", "1   As a dedicated professional with a strong back...   \n", "2   An IT/OT Engineer with a proven track record o...   \n", "3   Passionate about merging the worlds of technol...   \n", "4   Experienced IT Manager with over 15 years of e...   \n", "5   As a Product Development Lead at AFD Petroleum...   \n", "6   As a results-driven Senior Operations Executiv...   \n", "7                                                 NaN   \n", "8                                                 NaN   \n", "9                                                 NaN   \n", "10                                                NaN   \n", "11                                                NaN   \n", "12                                                NaN   \n", "13  \"Effective Leadership is putting first-things-...   \n", "14  After working 20 years in distribution of elec...   \n", "15  Doing my honours year in Agricultural Science ...   \n", "\n", "                                 position_description  \n", "0                                                 NaN  \n", "1                                                 NaN  \n", "2   Manufacturers Automation Inc. Is a value added...  \n", "3                                                 NaN  \n", "4   - Led the transition from a Managed Service Pr...  \n", "5                                                 NaN  \n", "6   Tasked with starting up the US-based operation...  \n", "7   - ERP Change Management Microsoft Business Dyn...  \n", "8                                                 NaN  \n", "9                                                 NaN  \n", "10                                                NaN  \n", "11                                                NaN  \n", "12                                                NaN  \n", "13                                                NaN  \n", "14  *Purchasing of electronic components. *Demand ...  \n", "15  Passionate subject matter expert on real time ...  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load data from both JSON files\n", "all_user_info = json.load(open(\"research/all_user_info.json\", \"r\"))\n", "\n", "# Convert to DataFrames\n", "df = pd.DataFrame(all_user_info)\n", "# df = df[df[\"about\"].notna()]\n", "df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>position</th>\n", "      <th>thinking</th>\n", "      <th>company</th>\n", "      <th>url</th>\n", "      <th>about</th>\n", "      <th>position_description</th>\n", "      <th>keywords</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>Director of Operations @ Manufacturers Automat...</td>\n", "      <td><PERSON>作为运营总监，负责销售和企业发展，具备采购决策影响力，并且与工业自动化...</td>\n", "      <td>ManuAuto</td>\n", "      <td>https://www.linkedin.com/in/mo-salehian-3b2033...</td>\n", "      <td>With over 20 years in Communication and indust...</td>\n", "      <td>NaN</td>\n", "      <td>加拿大工业物联网产品(MOXA)的代理商</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Area Sales Manager at Manufacturers Automation...</td>\n", "      <td><PERSON><PERSON>f是区域销售经理，专注于业务发展、工厂自动化、工业4.0和IIOT，直...</td>\n", "      <td>ManuAuto</td>\n", "      <td>https://www.linkedin.com/in/usman-ashraf-ab02b...</td>\n", "      <td>As a dedicated professional with a strong back...</td>\n", "      <td>NaN</td>\n", "      <td>加拿大工业物联网产品(MOXA)的代理商</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>Network Application Engineer</td>\n", "      <td><PERSON>作为网络应用工程师，可能参与技术选型和实施，虽然职位不高，...</td>\n", "      <td>ManuAuto</td>\n", "      <td>https://www.linkedin.com/in/kenny-dela-piedra-...</td>\n", "      <td>An IT/OT Engineer with a proven track record o...</td>\n", "      <td>Manufacturers Automation Inc. Is a value added...</td>\n", "      <td>加拿大工业物联网产品(MOXA)的代理商</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON></td>\n", "      <td>Product Development Manager</td>\n", "      <td><PERSON>作为产品开发经理，负责新产品的开发和技术选型，与我们的IoT解决...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/jeremia<PERSON>-burak-936...</td>\n", "      <td>Passionate about merging the worlds of technol...</td>\n", "      <td>NaN</td>\n", "      <td>加拿大储油设备 IoT 远程监控方案</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON></td>\n", "      <td>IT Manager | Power Platform Developer | Cybers...</td>\n", "      <td><PERSON>作为IT经理，负责公司内部的技术管理和安全，可能对IoT解决方案的技术...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/jldale?miniProfile...</td>\n", "      <td>Experienced IT Manager with over 15 years of e...</td>\n", "      <td>- Led the transition from a Managed Service Pr...</td>\n", "      <td>加拿大储油设备 IoT 远程监控方案</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Product Development Lead @ AFD Petroleum Ltd. ...</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Boparai作为产品开发负责人，专注于行业标准和问题解决，与我们的IoT解决...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/gurbaj-boparai-722...</td>\n", "      <td>As a Product Development Lead at AFD Petroleum...</td>\n", "      <td>NaN</td>\n", "      <td>加拿大储油设备 IoT 远程监控方案</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON></td>\n", "      <td>Project Manager, Organizational Improvement, L...</td>\n", "      <td><PERSON>作为项目经理，负责组织改进和产品支持，可能对IoT解决方案的实施...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/richard-loewen-793...</td>\n", "      <td>As a results-driven Senior Operations Executiv...</td>\n", "      <td>Tasked with starting up the US-based operation...</td>\n", "      <td>加拿大储油设备 IoT 远程监控方案</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>Senior Business Systems Analyst</td>\n", "      <td><PERSON>作为高级业务系统分析师，负责分析和优化业务系统，与IoT解决方案的集成...</td>\n", "      <td>AFD Petroleum Ltd</td>\n", "      <td>https://www.linkedin.com/in/alex-martin-453525...</td>\n", "      <td>NaN</td>\n", "      <td>- ERP Change Management Microsoft Business Dyn...</td>\n", "      <td>加拿大储油设备 IoT 远程监控方案</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td><PERSON></td>\n", "      <td>Global Product Categories- Ophardt Hygiene- Te...</td>\n", "      <td><PERSON>负责全球产品类别，可能对产品创新和技术集成有直接影响力，适合作为...</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/klaus-z<PERSON>pe-094...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>加拿大卫生间智能设备联网需求</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td><PERSON></td>\n", "      <td>Business Unit Director at OPHARDT Hygiene</td>\n", "      <td><PERSON>作为业务单元总监，可能负责整体业务战略和产品线决策，具备较高的决策权和...</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/fredroffel?miniPro...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>加拿大卫生间智能设备联网需求</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td><PERSON></td>\n", "      <td>Technical Operations Manager at OPHARDT Hygiene</td>\n", "      <td><PERSON>负责技术运营，可能对技术集成和设备联网有实际需求和影响力，适合作为...</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/joshua-geurts-3939...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>加拿大卫生间智能设备联网需求</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td><PERSON></td>\n", "      <td>Engineering Manager, Product Manager - <PERSON><PERSON><PERSON>...</td>\n", "      <td>Ted House作为工程和产品经理，直接负责产品的技术和管理，可能对智能设备联网有实际需求。</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/ted-house-8546282?...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>加拿大卫生间智能设备联网需求</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td><PERSON></td>\n", "      <td>Sales Manager BeNeLux / Internal Sales Manager...</td>\n", "      <td><PERSON>负责销售管理，可能对市场需求和产品推广有深入了解，能够影响采购决策。</td>\n", "      <td>OPHARDT Hygiene</td>\n", "      <td>https://www.linkedin.com/in/manfred-weenen-2bb...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>加拿大卫生间智能设备联网需求</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td><PERSON></td>\n", "      <td>Solutions Engineer🔃Actionable Results ➔ Operat...</td>\n", "      <td><PERSON>作为解决方案工程师，涉及多个行业的运营和研发，可能对物联网技术...</td>\n", "      <td><PERSON><PERSON><PERSON> (Home Energy Management)</td>\n", "      <td>https://www.linkedin.com/in/billchatterjee?min...</td>\n", "      <td>\"Effective Leadership is putting first-things-...</td>\n", "      <td>NaN</td>\n", "      <td>美国家庭用电配电盒cellular联网需求</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Operations Mgr-Curb</td>\n", "      <td><PERSON><PERSON>作为运营经理，可能负责相关项目的运营和管理，具备一定的决策影响力，适...</td>\n", "      <td><PERSON><PERSON><PERSON> (Home Energy Management)</td>\n", "      <td>https://www.linkedin.com/in/terri-morfin-a8817...</td>\n", "      <td>After working 20 years in distribution of elec...</td>\n", "      <td>*Purchasing of electronic components. *Demand ...</td>\n", "      <td>美国家庭用电配电盒cellular联网需求</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td><PERSON></td>\n", "      <td>Real Time Information and Transaction Specialist</td>\n", "      <td><PERSON> <PERSON>的职位涉及实时信息和交易，可能与电力管理和物联网技术相关，适合作为潜在联系人。</td>\n", "      <td><PERSON><PERSON><PERSON> (Home Energy Management)</td>\n", "      <td>https://www.linkedin.com/in/tim-ryan-212b9?min...</td>\n", "      <td>Doing my honours year in Agricultural Science ...</td>\n", "      <td>Passionate subject matter expert on real time ...</td>\n", "      <td>美国家庭用电配电盒cellular联网需求</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 name                                           position  \\\n", "0         Mo Salehian  Director of Operations @ Manufacturers Automat...   \n", "1        Usman <PERSON>  Area Sales Manager at Manufacturers Automation...   \n", "2   <PERSON>a Piedra                       Network Application Engineer   \n", "3      <PERSON>                        Product Development Manager   \n", "4         <PERSON>  IT Manager | Power Platform Developer | Cybers...   \n", "5      Gurbaj Boparai  Product Development Lead @ AFD Petroleum Ltd. ...   \n", "6      <PERSON>  Project Manager, Organizational Improvement, L...   \n", "7         <PERSON>                    Senior Business Systems Analyst   \n", "8      Klaus <PERSON>pe  Global Product Categories- Ophardt Hygiene- Te...   \n", "9         <PERSON>          Business Unit Director at OPHARDT Hygiene   \n", "10      <PERSON>    Technical Operations Manager at OPHARDT Hygiene   \n", "11          <PERSON>  Engineering Manager, Product Manager - <PERSON><PERSON><PERSON>...   \n", "12     <PERSON>  Sales Manager <PERSON><PERSON>e<PERSON>ux / Internal Sales Manager...   \n", "13    <PERSON>  Solutions Engineer🔃Actionable Results ➔ Operat...   \n", "14       Terri <PERSON>                                Operations Mgr-Curb   \n", "15           <PERSON>   Real Time Information and Transaction Specialist   \n", "\n", "                                             thinking  \\\n", "0   Mo Salehian作为运营总监，负责销售和企业发展，具备采购决策影响力，并且与工业自动化...   \n", "1   Usman Ashraf是区域销售经理，专注于业务发展、工厂自动化、工业4.0和IIOT，直...   \n", "2   <PERSON>作为网络应用工程师，可能参与技术选型和实施，虽然职位不高，...   \n", "3   Jeremiah Burak作为产品开发经理，负责新产品的开发和技术选型，与我们的IoT解决...   \n", "4   <PERSON>作为IT经理，负责公司内部的技术管理和安全，可能对IoT解决方案的技术...   \n", "5   Gurbaj Boparai作为产品开发负责人，专注于行业标准和问题解决，与我们的IoT解决...   \n", "6   <PERSON>作为项目经理，负责组织改进和产品支持，可能对IoT解决方案的实施...   \n", "7   <PERSON> Martin作为高级业务系统分析师，负责分析和优化业务系统，与IoT解决方案的集成...   \n", "8   <PERSON> Zscher<PERSON>负责全球产品类别，可能对产品创新和技术集成有直接影响力，适合作为...   \n", "9   <PERSON> Roffel作为业务单元总监，可能负责整体业务战略和产品线决策，具备较高的决策权和...   \n", "10  <PERSON> Geurts负责技术运营，可能对技术集成和设备联网有实际需求和影响力，适合作为...   \n", "11    Ted House作为工程和产品经理，直接负责产品的技术和管理，可能对智能设备联网有实际需求。   \n", "12   <PERSON> Weenen负责销售管理，可能对市场需求和产品推广有深入了解，能够影响采购决策。   \n", "13  <PERSON> Chatterjee作为解决方案工程师，涉及多个行业的运营和研发，可能对物联网技术...   \n", "14  Te<PERSON> Morfin作为运营经理，可能负责相关项目的运营和管理，具备一定的决策影响力，适...   \n", "15    <PERSON>的职位涉及实时信息和交易，可能与电力管理和物联网技术相关，适合作为潜在联系人。   \n", "\n", "                          company  \\\n", "0                        ManuAuto   \n", "1                        ManuAuto   \n", "2                        ManuAuto   \n", "3               AFD Petroleum Ltd   \n", "4               AFD Petroleum Ltd   \n", "5               AFD Petroleum Ltd   \n", "6               AFD Petroleum Ltd   \n", "7               AFD Petroleum Ltd   \n", "8                 OPHARDT Hygiene   \n", "9                 OPHARDT Hygiene   \n", "10                OPHARDT Hygiene   \n", "11                OPHARDT Hygiene   \n", "12                OPHARDT Hygiene   \n", "13  Curb (Home Energy Management)   \n", "14  Curb (Home Energy Management)   \n", "15  Curb (Home Energy Management)   \n", "\n", "                                                  url  \\\n", "0   https://www.linkedin.com/in/mo-salehian-3b2033...   \n", "1   https://www.linkedin.com/in/usman-ashraf-ab02b...   \n", "2   https://www.linkedin.com/in/kenny-dela-piedra-...   \n", "3   https://www.linkedin.com/in/jeremia<PERSON>-burak-936...   \n", "4   https://www.linkedin.com/in/jldale?miniProfile...   \n", "5   https://www.linkedin.com/in/gurbaj-boparai-722...   \n", "6   https://www.linkedin.com/in/richard-loewen-793...   \n", "7   https://www.linkedin.com/in/alex-martin-453525...   \n", "8   https://www.linkedin.com/in/klaus-z<PERSON><PERSON>-094...   \n", "9   https://www.linkedin.com/in/fredroffel?miniPro...   \n", "10  https://www.linkedin.com/in/joshua-geurts-3939...   \n", "11  https://www.linkedin.com/in/ted-house-8546282?...   \n", "12  https://www.linkedin.com/in/manfred-weenen-2bb...   \n", "13  https://www.linkedin.com/in/billchatterjee?min...   \n", "14  https://www.linkedin.com/in/terri-morfin-a8817...   \n", "15  https://www.linkedin.com/in/tim-ryan-212b9?min...   \n", "\n", "                                                about  \\\n", "0   With over 20 years in Communication and indust...   \n", "1   As a dedicated professional with a strong back...   \n", "2   An IT/OT Engineer with a proven track record o...   \n", "3   Passionate about merging the worlds of technol...   \n", "4   Experienced IT Manager with over 15 years of e...   \n", "5   As a Product Development Lead at AFD Petroleum...   \n", "6   As a results-driven Senior Operations Executiv...   \n", "7                                                 NaN   \n", "8                                                 NaN   \n", "9                                                 NaN   \n", "10                                                NaN   \n", "11                                                NaN   \n", "12                                                NaN   \n", "13  \"Effective Leadership is putting first-things-...   \n", "14  After working 20 years in distribution of elec...   \n", "15  Doing my honours year in Agricultural Science ...   \n", "\n", "                                 position_description               keywords  \n", "0                                                 NaN   加拿大工业物联网产品(MOXA)的代理商  \n", "1                                                 NaN   加拿大工业物联网产品(MOXA)的代理商  \n", "2   Manufacturers Automation Inc. Is a value added...   加拿大工业物联网产品(MOXA)的代理商  \n", "3                                                 NaN     加拿大储油设备 IoT 远程监控方案  \n", "4   - Led the transition from a Managed Service Pr...     加拿大储油设备 IoT 远程监控方案  \n", "5                                                 NaN     加拿大储油设备 IoT 远程监控方案  \n", "6   Tasked with starting up the US-based operation...     加拿大储油设备 IoT 远程监控方案  \n", "7   - ERP Change Management Microsoft Business Dyn...     加拿大储油设备 IoT 远程监控方案  \n", "8                                                 NaN         加拿大卫生间智能设备联网需求  \n", "9                                                 NaN         加拿大卫生间智能设备联网需求  \n", "10                                                NaN         加拿大卫生间智能设备联网需求  \n", "11                                                NaN         加拿大卫生间智能设备联网需求  \n", "12                                                NaN         加拿大卫生间智能设备联网需求  \n", "13                                                NaN  美国家庭用电配电盒cellular联网需求  \n", "14  *Purchasing of electronic components. *Demand ...  美国家庭用电配电盒cellular联网需求  \n", "15  Passionate subject matter expert on real time ...  美国家庭用电配电盒cellular联网需求  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a new column 'keywords' in df based on matching company names from cases\n", "df[\"keywords\"] = df[\"company\"].apply(\n", "    lambda company: next((case[\"keywords\"] for case in cases if case[\"company\"] == company), None)\n", ")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["姓名: <PERSON>\n", "职位: Director of Operations @ Manufacturers Automation (ManuAuto) | Sales, Entrepreneurship\n", "个人简介: With over 20 years in Communication and industrial automation, I bring a passion for operational excellence to Manufacturers Automation (ManuAuto). As a value-added distributor at the forefront of Industry 4.0, we're enabling smarter factories with future-proof solutions in Industrial IoT and 5G. We prioritize robust connected ecosystems with a focus on Cyber Security, and optimize data flow with cutting-edge Edge Connectivity solutions.My combined expertise in operations management and business development fuels my drive to cultivate strong relationships with leading brands like Moxa, Balluff, Weidmuller, Westermo and more. This allows me to foster win-win partnerships and drive efficiency across the entire value chain. I'm energized by ManuAuto's commitment to innovation, and together with our talented team, I'm dedicated to crafting solutions that propel our clients towards a productive and secure industrial future.\n", "职位描述: nan\n", "关键词: 加拿大工业物联网产品(MOXA)的代理商\n", "- 适合的原因：Mo Salehian在Manufacturers Automation (ManuAuto)担任运营总监，拥有超过20年的通信和工业自动化经验。他所在的公司是工业4.0的增值分销商，专注于工业物联网和5G解决方案，并且与Moxa等领先品牌建立了强大的合作关系。Mo在业务发展和运营管理方面的专业知识使他能够推动与领先品牌的合作关系，并提高整个价值链的效率。此外，他对创新的承诺和对构建安全、生产性工业未来的热情与我们的解决方案目标高度一致。\n", "\n", "- 是否适合作为潜在联系人：是。\n", "----------------------------------------------------------------------------------------------------\n", "姓名: <PERSON><PERSON>\n", "职位: Area Sales Manager at Manufacturers Automation (ManuAuto) Business Development | Factory Automation | Industry 4.0 | IIOT\n", "个人简介: As a dedicated professional with a strong background in Electrical and Control Engineering, I have cultivated a deep expertise in business development, technical sales, and automation, particularly within the realms of Industry 4.0. My career journey has been marked by a consistent drive to innovate and deliver results that elevate both client and company success.Currently, I serve as the Area Sales Manager at Manufacturers Automation Inc. (ManuAuto), where I lead efforts to bring cutting-edge automation solutions to businesses in Ontario. My role involves not just territory account management and product demonstration, but also forging lasting partnerships that drive the adoption of technologies such as Industrial Ethernet, Cybersecurity, Edge Connectivity, and IIoT.I am passionate about leveraging technology to solve complex business challenges and am always eager to engage in conversations about the future of industrial automation and connectivity. Connect with me if you’re interested in exploring how we can collaborate to drive innovation and growth in this exciting field.\n", "职位描述: nan\n", "关键词: 加拿大工业物联网产品(MOXA)的代理商\n", "- 适合的原因：\n", "  1. Usman Ashraf 拥有丰富的电气和控制工程背景，并在业务发展、技术销售和自动化领域积累了深厚的专业知识，这与工业物联网解决方案的技术需求高度相关。\n", "  2. 作为Manufacturers Automation Inc. (ManuAuto)的区域销售经理，他负责在安大略省推广先进的自动化解决方案，这表明他在该地区有广泛的行业联系和市场影响力。\n", "  3. 他的工作涉及工业以太网、网络安全、边缘连接和IIoT等技术，这些都是工业物联网解决方案的重要组成部分。\n", "  4. 他对工业自动化和连接性的未来充满热情，并积极寻求合作机会，这与我们寻找合作伙伴的目标一致。\n", "\n", "- 是否适合作为潜在联系人：\n", "  是。Usman Ashraf 的背景和当前角色使他成为一个非常合适的潜在联系人。他在工业自动化和IIoT领域的专业知识和市场经验，使他能够有效地理解和推广我们的解决方案。此外，他在安大略省的市场影响力和对技术创新的热情，可能为我们在该地区的业务拓展带来积极的推动力。\n", "----------------------------------------------------------------------------------------------------\n", "姓名: <PERSON>\n", "职位: Network Application Engineer\n", "个人简介: An IT/OT Engineer with a proven track record of providing technical support, Server and Network Administration. 14 Years of Extensive experienced working on complex Environment. Excellent skill in design and implementation of Network LAN, WLAN and WAN. Implementation and maintenance of Domain type of Network, Centralized user Management including but not limited to Active Directory and LDAP, E-commerce Website Development and Administration, Python Programming for  Networking and Cybersecurity.\n", "职位描述: Manufacturers Automation Inc. Is a value added Distributor of popular brands in the market. such as Moxa, Balluff, <PERSON>, Robustel, Proxim, Puls, Weidmuller, and IMO.\n", "关键词: 加拿大工业物联网产品(MOXA)的代理商\n", "- 适合的原因：Kenny Dela Piedra在Manufacturers Automation Inc.担任Network Application Engineer，该公司是Moxa等知名品牌的增值分销商。Kenny拥有丰富的IT/OT工程经验，尤其是在网络设计和实施方面的技能，这与工业物联网解决方案的技术需求高度相关。此外，他的工作涉及网络管理和技术支持，这使他能够理解和评估工业物联网产品的技术优势和应用场景。因此，Kenny在技术层面上具备与我们解决方案相关的专业知识，并且所在公司是Moxa的代理商，符合我们寻找的目标企业方向。\n", "\n", "- 是否适合作为潜在联系人：是。\n", "----------------------------------------------------------------------------------------------------\n", "姓名: <PERSON>\n", "职位: Product Development Manager\n", "个人简介: Passionate about merging the worlds of technology and innovation, I bring a robust background of electrical knowledge and automation, coupled with expertise in hardware design, team leadership, and seamless software integration. With a track record of successfully orchestrating projects from concept to completion, I thrive at the intersection of creativity and practicality.My journey began as an electrician, and over the years, I've transformed that enthusiasm into a career marked by turning complex challenges into streamlined solutions. From designing cutting-edge hardware systems to architecting intricate automation processes, I relish the opportunity to bring ideas to life.Beyond technical prowess, I've honed the art of managing and inspiring cross-functional teams. My leadership style fosters collaboration, innovation, and a shared commitment to achieving excellence. I firmly believe that a team empowered with the right tools and guidance can achieve remarkable feats.As I continue on this exciting journey, I eagerly seek new opportunities to contribute my expertise to visionary projects, collaborate with like-minded professionals, and drive technological advancements that shape the future.\n", "职位描述: nan\n", "关键词: 加拿大储油设备 IoT 远程监控方案\n", "- 适合的原因：Jeremiah Burak 拥有丰富的电气知识和自动化经验，这与我们的 IoT 远程监控解决方案的技术需求高度相关。他在硬件设计和软件集成方面的专业知识，能够帮助我们更好地理解和满足客户的技术需求。此外，他在项目管理和跨职能团队领导方面的经验，表明他有能力推动复杂项目的实施，这对于推动 IoT 解决方案的成功部署至关重要。Jeremiah 对技术创新的热情和对推动技术进步的兴趣，使他成为一个理想的合作伙伴或客户，能够理解和欣赏我们解决方案的价值。\n", "\n", "- 是否适合作为潜在联系人：是。\n", "----------------------------------------------------------------------------------------------------\n", "姓名: <PERSON>\n", "职位: IT Manager | Power Platform Developer | Cybersecurity Practitioner\n", "个人简介: Experienced IT Manager with over 15 years of expertise in IT leadership, strategic planning, and systems management. Adept at driving digital transformation, enhancing cybersecurity posture, and optimizing IT operations to support business growth. Proven track record of delivering complex IT projects on time and within budget, including mergers and acquisitions, network architecture, and cloud migrations. Strong leader skilled in managing cross-functional teams and fostering innovation. Certified in ITIL v4, Microsoft Power Platform, and Harvard Business School’s Leadership Principles. Seeking to leverage a deep technical background and leadership experience in a Director of IT role to deliver high-impact results.\n", "职位描述: - Led the transition from a Managed Service Provider to an Internal IT model, delivering equivalent or superior solutions while reducing costs by over 20%. - Implemented and configured key technologies, such as the Microsoft Defender stack, Intune, and ManageEngine ServiceDesk Plus Cloud - Successfully managed VoIP system migration. - Implemented responsive Helpdesk practices and prioritization strategies, reducing mean time to repair from 8 business days to 9.5 business hours. - Elevated customer satisfaction scores from an average of 7 to over 9. - Utilized Power Automate to develop various low-code automations and integrations. - Collaborated on a significant project migrating outdated business systems to Microsoft Business Central and a proprietary fuel industry management platform, resulting in streamlined operations. - Conducted R&D, developing a system using edge computing, local/cellular/satellite networks, Python, and fuel system probes to report on storage tank temperature, density, and volume. Integrated this solution into the proprietary fuel industry management platform. - Implemented cybersecurity controls, significantly increasing Microsoft Secure Score from 41% to over 86%. - Managed various Azure services, including Virtual Machines, Logic Apps, Functions, CosmosDB, Azure Backup, Network Security Groups, Peering, Sentinel, Meraki vMX for VPN connectivity, Key Vaults, Storage Accounts, and Azure Active Directory. - Successfully implemented Defender for Cloud recommendations for these services. - Experience implementing and maintaining Single Sign-On functionality - Hired and managed a System Administrator and Helpdesk Technician with a focus on continuous improvement and customer satisfaction while fostering learning opportunities, career growth, and open communication.\n", "关键词: 加拿大储油设备 IoT 远程监控方案\n", "- 适合的原因：\n", "  1. **行业相关经验**：<PERSON> Dale 在其职位描述中提到参与了一个重要项目，该项目涉及将过时的业务系统迁移到一个专有的燃料行业管理平台，并开发了一个系统来报告储油罐的温度、密度和体积。这表明他对燃料行业和储油设备有直接的经验，这与我们的加拿大储油设备 IoT 远程监控方案直接相关。\n", "  2. **技术能力**：他具备丰富的 IT 管理和技术开发经验，尤其是在边缘计算、网络和安全方面的能力，这对于实施和管理 IoT 解决方案至关重要。\n", "  3. **数字化转型和创新**：他有推动数字化转型和创新的记录，能够识别和实施新技术来优化业务运营，这与我们提供的 IoT 解决方案的目标一致。\n", "  4. **领导能力和项目管理**：他有成功管理复杂 IT 项目的经验，能够在预算内按时交付项目，这对于推动 IoT 解决方案的实施和成功至关重要。\n", "\n", "- 是否适合作为潜在联系人：是。  \n", "  Justin Dale 的背景和经验与我们的解决方案高度相关，他在燃料行业的直接经验和技术能力使他成为一个理想的潜在联系人，可以帮助推动我们的 IoT 远程监控方案在加拿大储油设备市场的应用。\n", "----------------------------------------------------------------------------------------------------\n", "姓名: <PERSON><PERSON><PERSON><PERSON>\n", "职位: Product Development Lead @ AFD Petroleum Ltd. | Industry Standards, Problem Solving, Job Planning\n", "个人简介: As a Product Development Lead at AFD Petroleum Ltd., I leverage my six years of experience as a Journeyman Instrumentation and Control Technician and my certifications in Electrical Systems, Industrial Automation, and Electronics Foundations to deliver innovative and efficient solutions for the oil and energy sector. I have a proven track record of planning and executing complex projects, identifying and mitigating hazards, and coaching and training new team members.My passion for continuous learning and improvement drives me to seek new challenges and opportunities to enhance my skills and knowledge. I have successfully retrofitted several equipment with new industry standards, leading to improved performance and safety. I have also been recognized for my integrity, social intelligence, and work ethics by my peers and managers. I am eager to apply my expertise and values to contribute to the growth and success of AFD Petroleum and its clients.\n", "职位描述: nan\n", "关键词: 加拿大储油设备 IoT 远程监控方案\n", "- 适合的原因：Gurbaj Boparai在AFD Petroleum Ltd.担任产品开发负责人，拥有丰富的油气行业经验，尤其是在工业自动化和电子系统方面的专业知识。他的工作涉及复杂项目的规划和执行，这与物联网解决方案的实施过程密切相关。此外，他在设备改造和行业标准方面的经验表明他对新技术和解决方案持开放态度，可能对IoT远程监控方案感兴趣。作为产品开发负责人，他可能对引入新技术以提高设备性能和安全性有决策权或影响力。\n", "\n", "- 是否适合作为潜在联系人：是\n", "----------------------------------------------------------------------------------------------------\n", "姓名: <PERSON>\n", "职位: Project Manager, Organizational Improvement, Lubricant Product Support\n", "个人简介: As a results-driven Senior Operations Executive, I provide strategic vision and leadership to engineer operational processes that drive business transformation, increase revenue, improve quality and accelerate growth for multimillion-dollar operations. My specialized expertise lies in project management, logistics operations, quality assurance and warehousing/distribution. Throughout my career, I am consistently recognized for combining strategic planning with focused execution to create sustainable, high-value organizations through leadership and performance management. HIGHLIGHTED QUALIFICATIONS: ✔️Keen ability to assess business objectives, develop project plans, assemble high-performance teams, and execute on project objectives to support and advance organizational objectives✔️Record of successfully driving continuous improvement processes and cost reduction strategies that enhance operational efficiency and ensure the highest levels of customer service✔️Demonstrated ability to provide vision and leadership in dynamic environments to execute impactful strategies that drive productivity and strengthen financial results of complex operations✔️Multi-lingual professional with fluency in Spanish, as well as broad international exposure gained from living in the US, Canada, and South AmericaSPECIALITIES: 🔹Operations Management 🔹Strategic Business Planning 🔹Performance Improvement🔹Continuous Improvement 🔹Business Startup Operations 🔹Cost Control / Reduction🔹Sales Growth Enablement 🔹Procurement / Purchasing 🔹Vendor Pricing Negotiations🔹Profitable Growth Strategy 🔹Budgeting / P&L Management🔹Inventory Control Processes\n", "职位描述: Tasked with starting up the US-based operations in Texas and report directly to the President and CEO regarding the performance of the US market. Key member of the senior leadership team leading efforts to gain a foothold in the Texas oil and gas market. • Provide strategic direction for the performance of fuel and lubricants product lines and support organizational growth objectives through new business development initiatives • Successfully delivered year-over-year fuel volume growth of 12% with a 98% margin increase and a 20% increase in the volume of lubricants while driving a 14% improvement in margins\n", "关键词: 加拿大储油设备 IoT 远程监控方案\n", "- 适合的原因：\n", "  1. <PERSON>在润滑油产品支持和组织改进方面的项目管理经验表明他对油气行业有深入的了解，这与我们的储油设备IoT远程监控方案直接相关。\n", "  2. 他在运营管理、战略业务规划和绩效改进方面的专长可以帮助推动新技术的实施和优化，这对于IoT解决方案的成功部署至关重要。\n", "  3. 他的工作职责包括为燃料和润滑油产品线提供战略方向，并通过新业务开发支持组织增长，这表明他可能对创新技术和解决方案持开放态度。\n", "  4. 他在美国和加拿大的工作经验以及对油气市场的深入了解，使他成为一个有价值的联系人，能够帮助我们在这些市场中拓展业务。\n", "\n", "- 是否适合作为潜在联系人：\n", "  是的，<PERSON>适合作为潜在联系人。他的行业经验、战略规划能力以及对新业务开发的关注，使他成为我们储油设备IoT远程监控方案的理想目标客户或渠道合作伙伴。\n", "----------------------------------------------------------------------------------------------------\n", "姓名: <PERSON>\n", "职位: Solutions Engineer🔃Actionable Results ➔ Operations☸Finance☸R&D☸MFG Electronics│Semiconductor│Aerospace│DoD│BioMed\n", "个人简介: \"Effective Leadership is putting first-things-first◈Effective Management is the discipline to carry it out\" Stephen Covey26y Leadership & Management⎮20y Start-up Business Development⎮12y International Business24y Technical Leadership⎮16y Budget/Proforma/Finance⎮14y Venture Capital/Equity Funding20y Manufacturing/Supply Chain⎮18y Program/Project Management⎮22y Partnerships/Alliances➜ Unique blend of Entrepreneurial Executive & take-charge Corporate Leader in Finance⚜Funding, Operations⚜Manufacturing, Engineering⚜Science, Legal⚜M&A🎯 Target success with Real Data⇄Provable Facts⇄Managed Process from pre-Ideation➾Incubation to Funding➾Launch (skeleton/stealth-mode) to full-scale Operations♻ Event Change Agent 👉 Re-Engineer❖Corporate Sale❖Shut-down❖Mergers❖Acquisition🐬 Deftly swims in water small & large, start-up to Fortune 500 Corps across Research┃Operations⇰Finance┃Revenue⇰Manufacturing┃Supply Chain⇰Partner/Alliances✈Domestic💫International scope in Corp Strategy►Facility Planning►Plant Operations►Program Oversight►Product Development►Engineering►Manufacturing⧩Market Segments⇛Adv. Optics◈Forensics◈Navigation◈Microscopy◈Life Sciences◈IC/Wafer Test◈Documentation◈Assembly & Test◈Cellular Biology◈Microelectronics◈Material Inspection◈Power Management◈Compression Imaging◈Security/Surveillance❒Management strengths⇉Strategy⦿Finance⦿Product Development⦿Project Management⦿Process Re-Engineering⦿Efficiency Improvement❒Soup-to-nuts Material & Process Management from Sourcing⏏Production⏏Distribution⏏Global Manufacturing Services⏏Site/Branch Ops⏏Quality System⏏Sustaining Eng⏏Test Engineering❒Program Manager for Product Dev & rapid Prototyping to volume Manufacturing❒Facility, Infrastructure, Implementation: Finance❚Accounting, Biz Dev❚Sales, Vendor Relations❚Channel Partners, R&D Labs❚Clean-rooms, Manufacturing❚Supply Chain❒Project Management｜IT Infrastructure｜ERP｜Legacy Systems ｜Implementation & Conversions❒Awarded 8 Patents (See Awards)\n", "职位描述: nan\n", "关键词: 美国家庭用电配电盒cellular联网需求\n", "- 适合的原因：\n", "  1. **丰富的技术和管理经验**：<PERSON> Chatterjee 拥有24年的技术领导经验和20年的制造/供应链经验，这表明他对技术解决方案和制造过程有深入的理解，这对于家庭用电配电盒的cellular联网需求非常相关。\n", "  2. **广泛的行业背景**：他的背景涵盖了电子、半导体、航空航天和生物医学等多个领域，这些领域都可能涉及到电力管理和联网技术。\n", "  3. **项目管理和产品开发能力**：他在项目管理和产品开发方面的经验可以帮助推动新技术的实施和市场化。\n", "  4. **合作伙伴关系和联盟经验**：他有22年的合作伙伴关系和联盟经验，这对于建立渠道合作伙伴关系非常有价值。\n", "  5. **国际业务和战略规划能力**：他的国际业务经验和战略规划能力可以帮助在全球范围内推广和实施解决方案。\n", "\n", "- 是否适合作为潜在联系人：是\n", "\n", "Bill Chatterjee 的丰富经验和多样化的背景使他成为一个理想的潜在联系人。他的技术和管理能力、行业知识以及合作伙伴关系经验都与我们的工业物联网解决方案的目标市场和需求高度相关。\n", "----------------------------------------------------------------------------------------------------\n", "姓名: <PERSON><PERSON>\n", "职位: Operations Mgr-Curb\n", "个人简介: After working 20 years in distribution of electronics components, I decided to change careers and move to the other side of the business.  Instead of being the seller of components, I am now the buyer of electronic components.  I now work with a start-up company, Curb, which has given me the opportunity to learn different facets of the company.  My main objective is purchasing, demand planning, etc.  I have been instrumental in implementing a shipping program so that Curb could start shipping production units.  Will soon be helping to implementing an ERP system.  Previously….  Stellar years of experience in sales of electronic components. Successfully managed over 50 accounts, including major companies which generated sales of over $24M.  Superb price negotiation skills. Fosters balance of a winning attitude and street smarts to serve the unique needs of customers.  Excellent customer interface skills, resourcefulness and attention to detail.  Highly efficient with great organizational skills.\n", "职位描述: *Purchasing of electronic components. *Demand planning and forecasting. *implemented program to start shipping production units. *Some accounting processes. *Some customer interface.\n", "关键词: 美国家庭用电配电盒cellular联网需求\n", "- 适合的原因：\n", "  1. <PERSON><PERSON>在电子元件的采购和需求规划方面有丰富的经验，这与我们提供的工业物联网解决方案中涉及的硬件采购和管理需求高度相关。\n", "  2. 她目前在一家初创公司Curb工作，该公司可能正在寻求创新的联网解决方案来优化其产品和服务，这与我们的家庭用电配电盒cellular联网需求相契合。\n", "  3. Terri在销售和客户界面方面有丰富的经验，这意味着她可能对新技术和解决方案持开放态度，并能够有效地评估和推动新产品的采用。\n", "  4. 她参与实施ERP系统，表明她对技术解决方案的实施和整合有一定的理解，这对于评估我们的物联网解决方案的价值和可行性非常有帮助。\n", "\n", "- 是否适合作为潜在联系人：是。\n", "----------------------------------------------------------------------------------------------------\n", "姓名: <PERSON>\n", "职位: Real Time Information and Transaction Specialist\n", "个人简介: Doing my honours year in Agricultural Science in Biometry in 1983 set me on an unexpected pathway as I received training in mainframes and programming and got involved in the earliest days of Personal Computers.After opening the first IBM PC Dealership outside of a metropolitan area in Dubbo, country NSW, I was approached to join Elders Pastoral (who were a client) to join their new videotext service - Farmlink - it would be the first online service launched in Australia.Exciting opportunities keep appearing and I was able to take them and developed a special skill set working in new technology and business development with a common theme of improving online information and transaction services.I worked closely with the Seven Network for many years - in a business partnership, as a consultant and advisor and in the development and management of new businesses.I'm lucky to have a supporting family, and to have worked with many leading experts in their fields.I've been particularly fortunate to have developed relationships to have great partners, contractors and staff that have helped with the various businesses I have been involved with.\n", "职位描述: Passionate subject matter expert on real time data and transaction business models. Believer in the value of #DTDEMM - Real Time Disaggregated Energy Metering and Monitoring - and it’s ability to deliver benefits for the individual and community. Earnest supporter of Community Batteries and V2H/G ad the right technologies to deliver customer and community value and consequently worried that small batteries are risk for homeowners. Deeply concerned about Equity and Energy Poverty.\n", "关键词: 美国家庭用电配电盒cellular联网需求\n", "- 适合的原因：Tim Ryan在实时数据和交易业务模型方面具有丰富的经验，并且对实时分散能源计量和监控（#DTDEMM）有深入的了解和信仰。他对社区电池和V2H/G技术的支持表明他对能源管理和分配有浓厚的兴趣，这与家庭用电配电盒的cellular联网需求密切相关。此外，他对能源贫困和公平的关注表明他可能对创新的能源解决方案持开放态度，这可能使他对我们的工业物联网解决方案感兴趣。\n", "\n", "- 是否适合作为潜在联系人：是。\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["prompt_template = \"\"\"\n", "[任务描述]\n", "你是一名专业的工业物联网解决方案销售助理，你的任务是寻找目标企业中最合适的潜在联系人，以便向他们推销我们的解决方案让其成为我们的直接使用客户或者渠道合作伙伴。\n", "你期望寻找的方向是：{keywords}\n", "\n", "我会向你提供用户的详细信息，包括他们的姓名、职位、个人简介等，你需要分析他们的个人简介，思考他们是否与我们的业务、解决方案相关，\n", "是否含有与解决方案行业相关的关键词，得出他们是否适合作为潜在联系人，并给出他们适合的原因。\n", "\n", "[用户信息]\n", "姓名：{name}\n", "职位：{position}\n", "个人简介：{about}\n", "职位描述：{position_description}\n", "\n", "[输出]\n", "- 适合的原因\n", "- 是否适合作为潜在联系人\n", "\"\"\"\n", "\n", "\n", "def analyze_about_description(userinfo: dict) -> str:\n", "    \"\"\"\n", "    分析关于描述\n", "    \"\"\"\n", "    print(f\"姓名: {userinfo['name']}\")\n", "    print(f\"职位: {userinfo['position']}\")\n", "    print(f\"个人简介: {userinfo['about']}\")\n", "    print(f\"职位描述: {userinfo['position_description']}\")\n", "    print(f\"关键词: {userinfo['keywords']}\")\n", "\n", "    content = \"\"\n", "    chain = ChatPromptTemplate.from_template(prompt_template) | llm\n", "    for chunk in chain.stream(input=userinfo):\n", "        content += chunk.content\n", "        # clear_output(wait=True)\n", "        # display(Markdown(content))\n", "        print(chunk.content, end=\"\")\n", "\n", "    print(\"\\n\" + \"-\" * 100)\n", "    return content\n", "\n", "\n", "records = df.to_dict(orient=\"records\")\n", "for record in records:\n", "    analyze_about_description(record)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize Playwright browser with cookies\n", "import json\n", "import os\n", "\n", "from playwright.async_api import async_playwright\n", "\n", "\n", "async def init_browser(cookie_path=None):\n", "    \"\"\"\n", "    Initialize a Playwright browser with cookies if provided\n", "\n", "    Args:\n", "        cookie_path: Path to the cookie file (JSON format)\n", "\n", "    Returns:\n", "        Tuple of (playwright, browser, context, page)\n", "    \"\"\"\n", "    print(\"Initializing browser...\")\n", "    playwright = await async_playwright().start()\n", "    browser = await playwright.chromium.launch(\n", "        # executable_path=\"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome\",\n", "        proxy={\"server\": \"http://127.0.0.1:1087\"},\n", "        headless=True,\n", "    )\n", "\n", "    # Create a context\n", "    context = await browser.new_context()\n", "\n", "    # Load cookies if provided\n", "    if cookie_path and os.path.exists(cookie_path):\n", "        try:\n", "            with open(cookie_path, \"r\") as f:\n", "                cookies = json.load(f)\n", "            await context.add_cookies(cookies)\n", "            print(f\"Loaded {len(cookies)} cookies from {cookie_path}\")\n", "        except Exception as e:\n", "            print(f\"Error loading cookies: {e}\")\n", "\n", "    # Create a new page\n", "    page = await context.new_page()\n", "\n", "    return playwright, browser, context, page\n", "\n", "\n", "async def close_browser(playwright, browser):\n", "    \"\"\"\n", "    Close browser and playwright\n", "    \"\"\"\n", "    await browser.close()\n", "    await playwright.stop()\n", "    print(\"<PERSON><PERSON><PERSON> closed\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "from playwright.async_api import Page\n", "\n", "\n", "# function to scroll down and load more profiles like a human would\n", "async def scroll_until_no_more_results(page: Page, limit: int = 100):\n", "    # find all profile cards to get a count\n", "    profile_cards = page.locator(\".org-people-profile-card__profile-card-spacing\")\n", "    profile_count = await profile_cards.count()\n", "    print(f\"Initial profile count: {profile_count}\")\n", "\n", "    previous_count = 0\n", "    current_count = await profile_cards.count()\n", "\n", "    # keep scrolling until no new profiles are loaded\n", "    while current_count > previous_count and current_count < limit:\n", "        previous_count = current_count\n", "\n", "        # check if there is a button to load more profiles\n", "        load_more_button = page.get_by_text(\"Show more results\")\n", "        if await load_more_button.is_visible():\n", "            await load_more_button.click()\n", "        else:\n", "            # scroll down to bottom of page\n", "            await page.evaluate(\"window.scrollTo(0, document.body.scrollHeight)\")\n", "\n", "        # pause like a human reading content (3-5 seconds)\n", "        await page.wait_for_timeout(1000 + random.randint(0, 2000))\n", "\n", "        # check if new profiles were loaded\n", "        current_count = await profile_cards.count()\n", "        print(f\"Current profile count: {current_count}\")\n", "\n", "    print(f\"Finished scrolling. Total profiles found: {current_count}\")\n", "    return current_count"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["async def get_peoples(page: Page):\n", "    peoples = []\n", "    for i in await page.locator(\".org-people-profile-card__profile-card-spacing:has(button)\").all():\n", "        title = (await i.locator(\".artdeco-entity-lockup__title\").text_content()).strip()\n", "        position = (await i.locator(\".artdeco-entity-lockup__subtitle\").text_content()).strip()\n", "        url = (await i.locator(\".artdeco-entity-lockup__title a\").get_attribute(\"href\")).strip()\n", "        print(\"name: \", title, \"position: \", position, \"url: \", url)\n", "        peoples.append({\"name\": title, \"position\": position, \"url\": url})\n", "    return peoples"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initializing browser...\n", "Loaded 1 cookies from cookies.json\n", "Initial profile count: 12\n", "Current profile count: 24\n", "Current profile count: 36\n", "Current profile count: 48\n", "Current profile count: 60\n", "Current profile count: 72\n", "Current profile count: 84\n", "Current profile count: 96\n", "Current profile count: 108\n", "Finished scrolling. Total profiles found: 108\n", "total profiles found: 108\n", "name:  <PERSON><PERSON> position:  Digital & IoT Technical Product Manager - Otis Elevator Co. url:  https://www.linkedin.com/in/kas-williams-9b630184?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABHXsFoBfjkDmXs4kuMpwnq_6QQAy3rk670\n", "name:  <PERSON><PERSON><PERSON> <PERSON> position:  Director & General Manager - MET, CIS & Qatar at Otis Elevator Co. url:  https://www.linkedin.com/in/youssef-a-wazni-942aa534?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAdHyQIBfQK2t-xwSNsvSVl2PC6dctW0Pms\n", "name:  <PERSON><PERSON><PERSON> position:  Driving operational excellence at Otis Elevator Co. url:  https://www.linkedin.com/in/prisha-nittur-a5bb05107?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABsJ26cBmyJlnccyXcW-Bg_vNVkd-8PYCTo\n", "name:  <PERSON><PERSON> position:  Field Operations Team Lead @ Otis Elevator Company | url:  https://www.linkedin.com/in/lijo-james-b8b47779?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABCe7NsBDdDVBjSErKm2mS9r0IKyOl-F2Sk\n", "name:  <PERSON><PERSON> position:  Project Engineer at Otis Elevator Co. specializing in Elevators and Escalators url:  https://www.linkedin.com/in/jansher-muhammed-3a626a2a?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAYW464Bi1q_CLJPqOEl3vUXW227UoJrTdk\n", "peoples: [{'name': '<PERSON><PERSON>', 'position': 'Digital & IoT Technical Product Manager - Otis Elevator Co.', 'url': 'https://www.linkedin.com/in/kas-williams-9b630184?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABHXsFoBfjkDmXs4kuMpwnq_6QQAy3rk670'}, {'name': 'Youssef <PERSON>', 'position': 'Director & General Manager - MET, CIS & Qatar at Otis Elevator Co.', 'url': 'https://www.linkedin.com/in/youssef-a-wazni-942aa534?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAdHyQIBfQK2t-xwSNsvSVl2PC6dctW0Pms'}, {'name': 'Prisha <PERSON>ttur', 'position': 'Driving operational excellence at Otis Elevator Co.', 'url': 'https://www.linkedin.com/in/prisha-nittur-a5bb05107?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABsJ26cBmyJlnccyXcW-Bg_vNVkd-8PYCTo'}, {'name': 'Lijo James', 'position': 'Field Operations Team Lead @ Otis Elevator Company |', 'url': 'https://www.linkedin.com/in/lijo-james-b8b47779?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABCe7NsBDdDVBjSErKm2mS9r0IKyOl-F2Sk'}, {'name': 'Jansher Muhammed', 'position': 'Project Engineer at Otis Elevator Co. specializing in Elevators and Escalators', 'url': 'https://www.linkedin.com/in/jansher-muhammed-3a626a2a?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAYW464Bi1q_CLJPqOEl3vUXW227UoJrTdk'}]\n", "Browser closed\n"]}], "source": ["playwright, browser, context, page = await init_browser(\"linkedin/cookies.json\")\n", "\n", "try:\n", "    await page.goto(\n", "        \"https://www.linkedin.com/company/otis_elevators/people/\",\n", "        timeout=30000,\n", "        wait_until=\"domcontentloaded\",\n", "    )\n", "\n", "    # save the page as image\n", "    await page.screenshot(path=\"page.png\")\n", "\n", "    total_profiles = await scroll_until_no_more_results(page, 100)\n", "    print(f\"total profiles found: {total_profiles}\")\n", "    peoples = await get_peoples(page)\n", "    print(f\"peoples: {peoples}\")\n", "finally:\n", "    await close_browser(playwright, browser)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initial profile count: 792\n", "Current profile count: 804\n", "Current profile count: 816\n", "Current profile count: 828\n", "Current profile count: 840\n", "Current profile count: 852\n", "Current profile count: 864\n", "Current profile count: 876\n", "Current profile count: 888\n", "Current profile count: 900\n", "Current profile count: 912\n", "Current profile count: 924\n", "Current profile count: 936\n", "Current profile count: 948\n", "Current profile count: 960\n", "Current profile count: 972\n", "Current profile count: 984\n", "Current profile count: 996\n", "Current profile count: 1000\n", "Current profile count: 1000\n", "Finished scrolling. Total profiles found: 1000\n"]}], "source": ["# execute the scrolling function\n", "total_profiles = await scroll_until_no_more_results()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name:  <PERSON><PERSON> position:  Digital & IoT Technical Product Manager - Otis Elevator Co. url:  https://www.linkedin.com/in/kas-williams-9b630184?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABHXsFoBfjkDmXs4kuMpwnq_6QQAy3rk670\n", "name:  <PERSON><PERSON> position:  Field Operations Team Lead @ Otis Elevator Company | url:  https://www.linkedin.com/in/lijo-james-b8b47779?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABCe7NsBDdDVBjSErKm2mS9r0IKyOl-F2Sk\n", "name:  <PERSON><PERSON><PERSON> <PERSON> position:  Director & General Manager - MET, CIS & Qatar at Otis Elevator Co. url:  https://www.linkedin.com/in/youssef-a-wazni-942aa534?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAdHyQIBfQK2t-xwSNsvSVl2PC6dctW0Pms\n", "name:  <PERSON><PERSON><PERSON> position:  Driving operational excellence at Otis Elevator Co. url:  https://www.linkedin.com/in/prisha-nittur-a5bb05107?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABsJ26cBmyJlnccyXcW-Bg_vNVkd-8PYCTo\n", "name:  <PERSON><PERSON> position:  Project Engineer at Otis Elevator Co. specializing in Elevators and Escalators url:  https://www.linkedin.com/in/jansher-muhammed-3a626a2a?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAYW464Bi1q_CLJPqOEl3vUXW227UoJrTdk\n", "name:  <PERSON> position:  Service Director U.A.E url:  https://www.linkedin.com/in/ahmad-hussein-69512149?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAopiJEBu_cuVc4CoVa0i5eFGS5DWkJguoA\n", "name:  <PERSON> position:  Chief Financial Officer url:  https://www.linkedin.com/in/marian-adel-46b50926?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAVqG1MB8wBLFvI9uO0O4nKBRZn5OeKvKHU\n", "name:  <PERSON><PERSON> position:  Project Engineer url:  https://www.linkedin.com/in/nandu-narayanadas-padma-272ba5a5?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABZsYTYBmcosbgctnMo6lBmR_unjr9zZOjA\n", "name:  <PERSON> position:  Finance Operations Manager, Global Finance Transformation & Strategic Financial Initiatives url:  https://www.linkedin.com/in/bettysebastian-849b3162?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAA1WYnkB7dBs8auuNhqpZy2rPdCI81rTOtE\n", "name:  <PERSON> position:  Senior Director & MD, Gulf, ME Territories & CIS Countries url:  https://www.linkedin.com/in/mohammed-al-qaisi-********?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAOwU-gB07vWHZd3zqAKm2MQkc8atA8SBho\n", "name:  <PERSON><PERSON><PERSON> position:  Service Sales Professional | Account Management | Strategy | Contracts url:  https://www.linkedin.com/in/meesam-mujtaba-714aa76b?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAA7vBUAB8od7kkXVgZ4Jope0Mf5QxDmb2ac\n", "name:  <PERSON> position:  Quality Coordinator at Otis Elevator Co. Vice chair of Middle East Otis Women’s Network url:  https://www.linkedin.com/in/sara-kalshamsi?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAC8k7acBIcrtyr2Sl-S6Lz9ReSxYHfRa8t8\n", "name:  <PERSON><PERSON><PERSON><PERSON>, PHR position:  Human Resources Professional url:  https://www.linkedin.com/in/jac<PERSON><PERSON>-lafaro-phr-a1257a8?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAGB_C4BNL5zfBOdFt2u73WKaBgkGVlWXHA\n", "name:  <PERSON><PERSON> position:  Senior Engineer-Modernization @ Otis Elevator Co. | Key Account Management, Sales & Product Management, Solutions Engineering url:  https://www.linkedin.com/in/sachin-sanathanan-********?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAumW2UBXvGjOnPqDtBsDykUkB19Mbbn4xg\n", "name:  <PERSON> position:  Finance Operations Manager, Global Finance Transformation & Strategic Financial Initiatives url:  https://www.linkedin.com/in/bettysebastian-849b3162?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAA1WYnkB7dBs8auuNhqpZy2rPdCI81rTOtE\n", "name:  <PERSON> position:  Senior Director & MD, Gulf, ME Territories & CIS Countries url:  https://www.linkedin.com/in/mohammed-al-qaisi-********?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAOwU-gB07vWHZd3zqAKm2MQkc8atA8SBho\n", "name:  <PERSON><PERSON><PERSON> I MBA I PMP® position:  Modernization Project Manager, UAE || Full time MBA from Durham University Business School || url:  https://www.linkedin.com/in/pradeep-venkatasamy-i-mba-i-pmp%C2%AE-b72a1311?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAJ2s8YB5IGYgUher8afpR8I_skTbSohSEI\n", "name:  <PERSON><PERSON><PERSON> position:  Service Sales Professional | Account Management | Strategy | Contracts url:  https://www.linkedin.com/in/meesam-mujtaba-714aa76b?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAA7vBUAB8od7kkXVgZ4Jope0Mf5QxDmb2ac\n", "name:  <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>(Mech) PMP position:  Project Manager / Operations Manager | Vertical Transportation | Construction | Infrastructure url:  https://www.linkedin.com/in/merwin-martis-pmp?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAYASSsBlQ0GnU-qV1Op92zF3x-RWdsL7uw\n", "name:  <PERSON><PERSON><PERSON><PERSON>, PHR position:  Human Resources Professional url:  https://www.linkedin.com/in/jac<PERSON><PERSON>-lafaro-phr-a1257a8?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAGB_C4BNL5zfBOdFt2u73WKaBgkGVlWXHA\n", "name:  <PERSON> position:  Talent Acqusition at OTIS url:  https://www.linkedin.com/in/carol-chang-8b668612?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAKXXYoBsPQKmZNZP9GS4U3ScFywsntbTpY\n", "name:  <PERSON> position:  Talent Acquisition Specialist OTIS Europe, Middle East & Africa url:  https://www.linkedin.com/in/elenatomesanchez?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAl8cO4BueJg5aJQVm-w7KcWNKCV5X41lfc\n", "name:  <PERSON><PERSON><PERSON> position:  Managing Director - Otis Saudi Arabia & Bahrain url:  https://www.linkedin.com/in/cihanozkaraman?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAkTVrcBehGK1ocmBYLnRBYGsKHgZfQp4S0\n", "name:  <PERSON><PERSON>. position:  Manager - Supply Chain url:  https://www.linkedin.com/in/manu-s-435a2819b?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAC71wmkBqo0Vy7okt4WDSQUUAsV2uFX53IU\n", "name:  <PERSON> position:  BU Supply Chain Manager @ Otis Elevator url:  https://www.linkedin.com/in/aaron-lane-a54aa363?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAA2CbCABimFTZpRxPZpkxbWOvb12dlqK58w\n", "name:  <PERSON> position:  Senior Director, HR Operations/ HR transformation/Hire-To-Retire at Otis Elevator Co. url:  https://www.linkedin.com/in/keith-lazaro-25752233?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAcEnOMBMW2TIdYRdGDHTVZkypxw4Cg4V28\n", "name:  <PERSON><PERSON> position:  Strategic Talent Acquisition Partner #BuildWhatsNext url:  https://www.linkedin.com/in/dejamartin?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABsdDtABf1OqWC3Tmolts2Yp7lYA8sjU8XI\n", "name:  <PERSON><PERSON> position:  Manager url:  https://www.linkedin.com/in/lina-h-94a48514b?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAACQzDwoBsWLkxIEuMERYyAq_JIGRa2rgUfY\n", "name:  <PERSON> position:  Talent Acquisition Partner @ Otis | Candidate Engagement, Talent Recruiting url:  https://www.linkedin.com/in/brenda-espinal-45233056?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAvLeX4BV_FQ49vdUgkG-3yZAJTJplf9P2s\n", "name:  <PERSON> position:  Chair & CEO, Otis Elevator Co. url:  https://www.linkedin.com/in/judy-marks-otis?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAABaNRqMByixIcbJNMSDvMrOxHbmZnmyi72A\n", "name:  <PERSON><PERSON><PERSON>,PMP position:  RIADH url:  https://www.linkedin.com/in/khaled-mohammed-pmp-3a9a961a8?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAADCR-7EBbD_N_2kbeCSJ-2joz9sMa0wXgg4\n"]}], "source": ["peoples = []\n", "for i in await page.locator(\".org-people-profile-card__profile-card-spacing:has(button)\").all():\n", "    title = (await i.locator(\".artdeco-entity-lockup__title\").text_content()).strip()\n", "    position = (await i.locator(\".artdeco-entity-lockup__subtitle\").text_content()).strip()\n", "    url = (await i.locator(\".artdeco-entity-lockup__title a\").get_attribute(\"href\")).strip()\n", "    print(\"name: \", title, \"position: \", position, \"url: \", url)\n", "    peoples.append({\"name\": title, \"position\": position, \"url\": url})"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'thinking': \"我现在正在寻找推销工业物联网 IoT 解决方案的潜在联系人。根据思考策略，这个解决方案的解决痛点是提高生产效率、降低成本、改善安全管理等，对应的目标行业和客户画像是电梯制造和服务行业。因此我需要寻找与技术选型、业务改善、项目管理相关的潜在联系人。我将重点关注职位关键词包含'技术产品经理'、'项目管理'、'服务'、'现代化'、'解决方案工程'的联系人。\",\n", " 'contacts': [{'name': '<PERSON><PERSON>',\n", "   'position': 'Digital & IoT Technical Product Manager - Otis Elevator Co.',\n", "   'thinking': '<PERSON><PERSON> <PERSON>的职位直接涉及IoT技术产品管理，具备对物联网解决方案的技术理解和决策影响力，是理想的潜在联系人。'},\n", "  {'name': '<PERSON><PERSON>',\n", "   'position': 'Senior Engineer-Modernization @ Otis Elevator Co. | Key Account Management, Sales & Product Management, Solutions Engineering',\n", "   'thinking': '<PERSON><PERSON>负责现代化工程和解决方案工程，涉及产品管理和销售，能够影响技术选型和业务改善决策。'},\n", "  {'name': '<PERSON><PERSON><PERSON> I MBA I PMP®',\n", "   'position': 'Modernization Project Manager, UAE || Full time MBA from Durham University Business School ||',\n", "   'thinking': '<PERSON><PERSON><PERSON>作为现代化项目经理，负责项目管理和技术更新，具备对新技术引入的影响力。'},\n", "  {'name': '<PERSON><PERSON><PERSON>',\n", "   'position': 'Service Sales Professional | Account Management | Strategy | Contracts',\n", "   'thinking': '<PERSON><PERSON><PERSON>在服务销售和战略管理方面有丰富经验，能够影响采购决策和客户关系管理。'},\n", "  {'name': '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>(Mech) PMP',\n", "   'position': 'Project Manager / Operations Manager | Vertical Transportation | Construction | Infrastructure',\n", "   'thinking': '<PERSON><PERSON><PERSON>负责项目和运营管理，涉及垂直运输和基础设施，能够影响技术和运营决策。'}]}"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["evaluate_people_list(peoples, \"工业物联网 IoT 解决方案\")"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/plain": ["31"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["len(peoples)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import functools\n", "import threading\n", "\n", "# Use a lock for thread safety in case the decorated function is called concurrently\n", "_singleton_lock = threading.Lock()\n", "\n", "\n", "def lazy_singleton(factory_func):\n", "    \"\"\"\n", "    A decorator that converts a factory function into a lazy-initialized singleton.\n", "\n", "    The decorated function will be called only once on the first access\n", "    to create the instance. Subsequent calls will return the same instance.\n", "    This implementation is thread-safe.\n", "\n", "    Args:\n", "        factory_func: The function that creates the instance (your factory).\n", "\n", "    Returns:\n", "        A wrapper function that provides the singleton instance.\n", "    \"\"\"\n", "    # Use a list or dictionary to hold the instance so it can be modified\n", "    # within the closure, or use a simple flag and the functools.wraps\n", "    # object itself to store the instance.\n", "    # Let's use an attribute on the wrapper function object for simplicity.\n", "    # Using None initially to indicate the instance hasn't been created.\n", "    # We need a way to differentiate None as a valid return value from\n", "    # the initial uninitialized state. A sentinel value or a separate flag is better.\n", "    # Let's use a flag.\n", "\n", "    # Attach attributes to the wrapper function object itself\n", "    @functools.wraps(factory_func)\n", "    def wrapper(*args, **kwargs):\n", "        # Check if the instance has already been created.\n", "        # We check for a special attribute on the wrapper function itself.\n", "        # Using hasattr is generally safer than checking for None directly\n", "        # if None could be a valid return value of the factory_func.\n", "        if not hasattr(wrapper, \"_lazy_singleton_instance\"):\n", "            # Acquire the lock to ensure thread-safe initialization\n", "            with _singleton_lock:\n", "                # Double-check after acquiring the lock in case another thread\n", "                # initialized it just before this thread acquired the lock.\n", "                if not hasattr(wrapper, \"_lazy_singleton_instance\"):\n", "                    print(f\"Creating instance using factory function: {factory_func.__name__}...\")\n", "                    try:\n", "                        # Call the original factory function to create the instance\n", "                        instance = factory_func(*args, **kwargs)\n", "                        # Store the created instance on the wrapper function object\n", "                        wrapper._lazy_singleton_instance = instance\n", "                    except Exception as e:\n", "                        print(f\"Error creating singleton instance: {e}\")\n", "                        # Optionally, re-raise the exception or handle it\n", "                        raise\n", "        # Return the stored instance\n", "        return wrapper._lazy_singleton_instance\n", "\n", "    return wrapper\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating instance using factory function: create_agent...\n", "creating agent\n", "88\n", "88\n", "88\n", "88\n", "88\n"]}], "source": ["import random\n", "\n", "\n", "class Agent:\n", "    foo: int\n", "\n", "    def __init__(self):\n", "        self.foo = random.randint(1, 100)\n", "\n", "\n", "@lazy_singleton\n", "def create_agent():\n", "    print(\"creating agent\")\n", "    return Agent()\n", "\n", "\n", "print(create_agent().foo)\n", "print(create_agent().foo)\n", "print(create_agent().foo)\n", "print(create_agent().foo)\n", "print(create_agent().foo)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}