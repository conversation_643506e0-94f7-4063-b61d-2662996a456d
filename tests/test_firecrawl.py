from agent.tools.firecrawl_tools import firecrawl_scrape


async def test_firecrawl_scrape():
    result = await firecrawl_scrape.ainvoke(
        input={
            "explanation": "This tool is used to scrape the content of the page.",
            "query": "test",
            "urls": [
                "https://altizon.com/datonis-iiot-platform/",
                "https://altizon.com/datonis-edge/",
            ],
        }
    )
    print(result)
    assert result is not None
    assert "Altizon" in result
