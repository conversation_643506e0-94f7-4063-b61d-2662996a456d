import json
import uuid

from loguru import logger

from server.data_processor import DataProcessor
from server.task.task_callback_handler import task_callback
from utils import load_env


async def test_contacts_agent():
    load_env()

    agent = DataProcessor(task_id=str(uuid.uuid4()))

    # with task_context(task_id="test_contacts_agent"):
    with task_callback(task_id="test_contacts_agent"):
        # result = await agent.ainvoke(input={"account_id": "3091799000303764040"})
        from agent.contacts_agent import ContactsAgent

        try:
            result = await ContactsAgent().get_contacts(
                user_query="帮我找出目标企业下最合适的联系人，要求联系人是New Zealand地区的。你只需要返回任意一个联系人即可",
                organization_id="54a1396069702d2185ec3900",
            )
        except Exception as e:
            logger.opt(exception=e).error("获取联系人失败 {}", e, exc_info=True)
            return

    if isinstance(result, Exception):
        print(result)
        return
    print(json.dumps(result, indent=4, ensure_ascii=False))
