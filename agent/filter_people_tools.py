import json
from typing import Annotated

from langchain_core.callbacks import dispatch_custom_event
from langchain_core.messages import ToolMessage
from langchain_core.output_parsers import JsonOutputParser, StrOutputParser
from langchain_core.prompts import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate,
)
from langchain_core.tools import InjectedToolCallId, tool
from langgraph.prebuilt import InjectedState
from langgraph.types import Command

from agent.contacts_agent_state import ContactsAgentState
from config import MODEL
from utils.file_handler import load_file
from utils.logger import get_logger
from utils.models import init_model

logging = get_logger(__name__)


@tool(parse_docstring=True)
async def think_people_search_strategy(
    user_query: str,
    organization_context: str,
    state: Annotated[ContactsAgentState, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId],
):
    """分析用户需求和目标组织情况，制定最优的联系人搜索策略。

    当需要在特定组织中查找联系人时使用此函数，它会综合考虑用户的具体要求、组织规模、行业特点等因素，
    制定针对性的搜索方案。适用于复杂的联系人查找任务，特别是当组织规模较大或用户需求较为具体时。
    不适用于简单的通用联系人搜索或已明确知道搜索关键词的情况。

    Args:
        user_query: 用户的原始需求描述，必须保持原样不得修改或自动生成建议，包含用户想要查找的联系人类型、职位、部门等具体要求
        organization_context: 目标组织的详细背景信息，包含员工数量、行业类型、组织结构、业务范围等关键信息，用于制定搜索策略
        state: 系统注入的状态参数，用于维护会话上下文和执行状态
        tool_call_id: 系统注入的工具调用标识符，用于追踪和管理函数调用

    Returns:
        详细的联系人搜索策略方案，包含推荐的搜索关键词、搜索优先级、预期结果数量范围、搜索注意事项等，
        为后续的具体搜索操作提供明确指导"""  # noqa: E501
    logging.info("正在思考搜索策略...")

    prompt_template = load_file("prompts/think_people_search_strategy_prompt.md")
    prompt = ChatPromptTemplate.from_template(prompt_template)

    llm = init_model(model=MODEL, temperature=0.1)
    chain = prompt | llm | StrOutputParser()

    invoke_params = {"user_query": user_query, "organization_context": organization_context}

    result = await chain.ainvoke(input=invoke_params)

    # 返回 Command 对象，更新 state
    return Command(
        update={
            "search_strategy": result,
            "messages": [ToolMessage(content=result, tool_call_id=tool_call_id)],
        }
    )


@tool(parse_docstring=True)
async def filter_people(people_list: list[dict], state: Annotated[ContactsAgentState, InjectedState]) -> str:
    """Filter and select appropriate contacts from a people list based on predefined search strategy.

    Use this function after obtaining a list of people to identify the most relevant contacts
    according to your targeting criteria. This helps prioritize outreach efforts by focusing
    on contacts that best match your strategic requirements. The filtering is based on the
    search strategy stored in the system state, which may include criteria like seniority levels,
    specific job functions, company size preferences, or other targeting parameters.

    Args:
        people_list: List of dictionaries where each person record contains at minimum
            "name" (full name), "title" (job title/position), and "linkedin_url" (LinkedIn profile URL).
            Additional fields in the dictionaries will be considered during filtering if relevant
            to the search strategy
        state: System injected state

    Returns:
        JSON string containing a list of LinkedIn URLs for the filtered contacts that best
        match the search strategy criteria. Only the URLs of selected contacts are returned
        for subsequent processing or outreach activities
    """
    if not people_list:
        logging.warning("人员列表为空，无法进行筛选")
        return json.dumps([], ensure_ascii=False)

    result = filter_contacts(
        search_strategy=state["search_strategy"],
        people_list=people_list,
    )

    # generate explanation for the filtering result
    # explanation = f"Based on the search strategy, I have filtered {len(result)} potential contacts from {len(people_list)} candidates"
    # dispatch_custom_event("thinking", explanation)

    return json.dumps(result, ensure_ascii=False)


def filter_contacts(search_strategy: str, people_list: list[dict], max_count: int = 20) -> list[str]:
    """根据搜索策略和人员列表，筛选出合适的潜在联系人。

    Args:
        search_strategy: 搜索策略
        people_list: 人员列表

    Returns:
        返回联系人linkedin_url列表
    """
    logging.info("正在筛选人员列表...")
    if people_list is None or len(people_list) == 0:
        logging.warning("人员列表为空")
        return []

    logging.info(f"人员列表数量：{len(people_list)}")

    if len(people_list) <= max_count:
        return [person["linkedin_url"] for person in people_list]

    try:
        # 准备输入
        system_prompt = load_file("prompts/filter_people_prompt.md")
        user_prompt = "请帮我筛选出合适的潜在联系人。\n人员列表：{people_list}"

        # 构建提示词模板
        prompts = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(system_prompt),
                HumanMessagePromptTemplate.from_template(user_prompt),
            ]
        )
        llm = init_model(MODEL)
        chain = prompts | llm | JsonOutputParser()

        # 构建调用参数
        invoke_params = {
            "search_strategy": search_strategy,
            "people_list": json.dumps(people_list, ensure_ascii=False),
            "max_count": max_count,
        }

        result = chain.invoke(input=invoke_params)
        logging.info(f"从{len(people_list)}人中筛选出{len(result)}人")

        dispatch_custom_event("thinking", f"{len(result)} potential contacts identified")
        return result

    except Exception as e:
        logging.error(f"过滤人员过程发生错误: {str(e)}")
        return []


if __name__ == "__main__":
    import asyncio

    user_query = "帮我找出Direct Control Limited（新西兰）下最合适的联系人，要求联系人是New Zealand地区的。"  # noqa: E501
    organization_context = "Direct Control Limited是一家新西兰本地的智能建筑集成解决方案提供商，员工约38人，专注于楼宇自动化、HVAC控制、照明控制、能源计量、IoT传感器、安全系统和数据分析等领域，服务对象包括地产开发商、业主、建筑师、顾问和承包商，业务涵盖咨询、设计、实施到运维全流程。"  # noqa: E501

    input_data = {"user_query": user_query, "organization_context": organization_context}
    result = asyncio.run(think_people_search_strategy.ainvoke(input=input_data))
    print(result)
