import asyncio

from langchain_core.callbacks import dispatch_custom_event
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent

from agent.account.models import CompanyBasicInfo
from agent.tools import (
    firecrawl_scrape,
    tavily_crawl,
    tavily_search,
)
from agent.tools.linkedin import get_company_details
from utils.file_handler import load_file
from utils.logger import get_logger
from utils.models import init_model

logging = get_logger(__name__)


async def research_company(company_basic_info: CompanyBasicInfo, user_query: str) -> dict:
    """
    调研单个公司的详细信息

    Args:
        company_basic_info: 公司基础信息
        user_query: 用户查询需求
    Returns:
        dict: 调研结果
        {
            "name": "公司名称",
            "research_result": "调研结果",
            "error": "错误信息",
        }
    """
    company_name = company_basic_info.name
    try:
        # 校验参数
        if not company_name:
            raise ValueError("Missing company name")
        if not company_basic_info.website_url:
            raise ValueError("Missing company website_url")
        if not user_query:
            raise ValueError("Missing user_query")

        logging.debug(f"开始调研候选公司: {company_name}")
        _send_research_message(company_name, "Starting in-depth research...")

        llm = init_model(
            model="gemini-2.5-pro",
            max_tokens=10240,
            temperature=0,
            thinking_budget=256,
            include_thoughts=True,
        )

        research_tools = [
            tavily_search,
            tavily_crawl,
            firecrawl_scrape,
            get_company_details,
        ]

        # 构建提示词和输入
        system_prompt = load_file("agent/account/prompts/research_company.md").format(user_query=user_query)
        prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")])
        agent = create_react_agent(model=llm, tools=research_tools, prompt=prompt)

        input_message = (
            f"用户查询需求：\n{user_query}\n\n待调研候选公司：\n{company_basic_info.model_dump_json(exclude_none=True)}"
        )

        result = await agent.ainvoke(
            input={"messages": [("human", input_message)]},
            config=RunnableConfig(metadata={"source": "research_company", "company_name": company_name}),
        )

        # 提取最终结果
        message = result["messages"][-1]
        if isinstance(message, AIMessage) and message.content:
            research_result = message.text()

            logging.debug(f"候选公司调研完成: {company_name}")
            _send_research_message(company_name, "I have completed the in-depth research.")

            return {"name": company_name, "research_result": research_result}
        else:
            raise Exception("No valid research result")

    except Exception as e:
        error_msg = f"failed to research company {company_name}: {str(e)}"
        logging.error(error_msg)
        return {"name": company_name, "error": error_msg}


def _send_research_message(company_name: str, message: str):
    """发送调研步骤消息"""
    try:
        dispatch_custom_event(
            "research_company",
            {"company_name": company_name, "message": message},
        )
    except Exception as e:
        # 当没有运行上下文时（如直接运行测试），使用 logging 记录消息
        logging.info(f"[{company_name}] {message}")
        logging.debug(f"dispatch_custom_event failed: {str(e)}")


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()
    # 测试数据
    company_basic_info = CompanyBasicInfo(
        id="54a1289e69702d979c383501",
        name="Inductive Automation",
        website_url="http://www.inductiveautomation.com",
        linkedin_url="http://www.linkedin.com/company/inductive-automation",
        organization_revenue="20.5M",
        phone="******-266-7798",
        founded_year=2003,
    )
    user_query = "I want to find some companies similar Telsim that use 5G mobile networks"  # noqa: E501

    result = asyncio.run(research_company(company_basic_info, user_query))
    print(result)
