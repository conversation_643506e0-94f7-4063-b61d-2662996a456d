import asyncio
import json
from typing import List, NotRequired, Optional, TypedDict

from langchain.callbacks.base import BaseCallbackHandler
from langchain_core.callbacks import dispatch_custom_event
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.graph.state import CompiledStateGraph

from agent.account.extract_research_result import ExtractResearchResult, extract_research_results
from agent.account.models import (
    CompanyBasicInfo,
    SimilarCompaniesResult,
)
from agent.account.research_company import research_company
from agent.account.search_similar_companies import search_similar_companies
from utils.logger import get_logger

logging = get_logger(__name__)


class ResearchState(TypedDict):
    """调研状态数据结构"""

    # 输入信息
    user_query: str  # 用户查询需求
    company_info: dict  # 模板公司信息
    excludes: NotRequired[List[str]]  # 排除的公司列表

    # 业务数据
    similar_companies: NotRequired[List[CompanyBasicInfo]]  # 相似公司列表
    research_results: NotRequired[List[str]]  # 调研结果

    # 结果数据
    title: NotRequired[str]  # 任务标题
    extract_result: NotRequired[ExtractResearchResult]  # 提取的结构化结果


class ResearchStateGraph:
    """调研工作流状态图"""

    def __init__(self, callbacks: Optional[list[BaseCallbackHandler]] = None):
        self.callbacks = callbacks
        self.graph = self._create_graph()

    def _create_graph(self) -> CompiledStateGraph:
        """创建状态图"""
        workflow = StateGraph(ResearchState)

        # 添加业务节点
        workflow.add_node("search_similar_companies", self._search_similar_companies_node)
        workflow.add_node("research_companies", self._research_companies_node)
        workflow.add_node("extract_results", self._extract_results_node)

        # 设置入口点
        workflow.set_entry_point("search_similar_companies")

        # 设置边
        workflow.add_edge("search_similar_companies", "research_companies")
        workflow.add_edge("research_companies", "extract_results")
        workflow.add_edge("extract_results", END)

        graph = workflow.compile()

        # 添加callbacks
        if self.callbacks and len(self.callbacks) > 0:
            graph = graph.with_config(config=RunnableConfig(callbacks=self.callbacks))

        return graph

    async def _search_similar_companies_node(self, state: ResearchState) -> ResearchState:
        """搜索相似公司节点"""
        company_name = state["company_info"].get("name", "the template company")
        dispatch_custom_event(
            "thinking",
            f"I will search for some companies similar to {company_name} and research them. Next, I will gradually achieve this goal.",
        )

        similar_companies: SimilarCompaniesResult = await search_similar_companies(
            user_query=state["user_query"], company_info=state["company_info"], excludes=state["excludes"]
        )
        state["title"] = similar_companies.title
        state["similar_companies"] = similar_companies.companies

        company_names = ", ".join([company.name for company in similar_companies.companies])
        dispatch_custom_event(
            "thinking", f"I have searched for {len(state['similar_companies'])} similar companies: {company_names}."
        )
        return state

    async def _research_companies_node(self, state: ResearchState) -> ResearchState:
        """深度调研候选公司节点"""
        similar_companies = state["similar_companies"]
        if len(similar_companies) == 0:
            raise ValueError("similar_companies is empty")

        logging.info(f"开始深度调研候选公司，共 {len(similar_companies)} 家公司")

        company_names = ", ".join([company.name for company in similar_companies])
        dispatch_custom_event(
            "thinking",
            f"Next, I will conduct in-depth research on these {len(similar_companies)} companies: {company_names}",
        )

        # 并行调研所有候选公司
        tasks = [
            research_company(company_basic_info=company, user_query=state["user_query"])
            for company in similar_companies
        ]
        research_results = await asyncio.gather(*tasks)

        # 过滤掉错误结果，返回调研结果
        research_results = [result.get("research_result") for result in research_results if "error" not in result]
        if len(research_results) == 0:
            raise Exception("research results is empty")

        logging.info(f"深度调研候选公司完成，共获得 {len(research_results)} 家公司的调研结果")
        state["research_results"] = research_results

        dispatch_custom_event(
            "thinking", f"Okay, I have completed the in-depth research on {len(research_results)} candidate companies."
        )
        return state

    async def _extract_results_node(self, state: ResearchState) -> ResearchState:
        """提取调研结果节点"""
        dispatch_custom_event(
            "thinking", "Next, I will extract information from the research results of candidate companies."
        )

        extract_result: ExtractResearchResult = await extract_research_results(
            research_results=state["research_results"]
        )
        state["extract_result"] = extract_result

        dispatch_custom_event(
            "thinking",
            "Now, I have got the final result, and I will show it to you soon.",
        )
        return state

    async def execute(self, initial_state: ResearchState) -> dict:
        """执行调研工作流"""
        logging.info("开始执行调研工作流...")
        try:
            final_state: ResearchState = await self.graph.ainvoke(initial_state)
            logging.info("调研工作流执行完成")

            # 校验返回结果
            if not final_state["extract_result"]:
                raise Exception("No valid research result")

            extract_result: ExtractResearchResult = final_state["extract_result"]
            result_data = extract_result.model_dump().get("data", [])

            return {
                "title": final_state["title"],
                "data": result_data,
            }
        except Exception as e:
            logging.error(f"调研工作流执行失败: {str(e)}")
            raise Exception(f"execute research workflow failed: {str(e)}")


async def execute_research_workflow(
    user_query: str,
    company_info: dict,
    excludes: list[str],
    callbacks: Optional[list[BaseCallbackHandler]] = None,
) -> dict:
    """
    执行调研工作流

    Args:
        user_query: user query, customer prompt
        company_info: CompanyInfo
        excludes: list of company names to exclude
        callbacks: list of callback handlers, used to handle the event stream

    Returns:
        dict: 调研结果
        {
            "title": "...", # 标题
            "data": [] # 调研结果
        }
    """
    company_name = company_info.get("name")
    if not company_name and not company_info.get("website"):
        raise Exception("Missing company name or website")

    # 排除当前公司
    if len(excludes) == 0 or company_name not in excludes:
        excludes.append(company_name)

    necessary_info = {"name": company_name, "website": company_info.get("website")}
    logging.info(
        f"开始执行调研任务, company_name: {necessary_info}, user_query: {user_query}, excludes: {excludes}"  # noqa: E501
    )

    initial_state = ResearchState(
        user_query=user_query,
        company_info=necessary_info,
        excludes=excludes,
    )
    research_state_graph = ResearchStateGraph(callbacks=callbacks)
    return await research_state_graph.execute(initial_state)


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()
    # 运行示例
    user_query = "我想找一些像Cirrus Link Solutions这样的需要使用边缘网关产品的Industrial IoT公司，我想把我们的边缘网关产品卖给他们"  # noqa: E501
    company_info = {"name": "Cirrus Link Solutions", "website": "http://www.cirrus-link.com/"}
    excludes = ["Cirrus Link Solutions", "Ingersoll Rand", "Inhand Networks"]

    result = asyncio.run(execute_research_workflow(user_query, company_info, excludes))
    print(json.dumps(result, ensure_ascii=False, indent=4))
