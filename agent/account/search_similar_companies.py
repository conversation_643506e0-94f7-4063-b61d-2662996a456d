import asyncio
import json
from typing import List, Optional

from langchain_core.prompts import ChatPromptTemplate
from langgraph.prebuilt import create_react_agent

from agent.account.models import SimilarCompaniesResult
from agent.tools.apollo import search_organizations
from agent.tools.linkedin import get_company_details
from utils.file_handler import load_file
from utils.logger import get_logger
from utils.models import init_model

logging = get_logger(__name__)


async def search_similar_companies(
    user_query: str, company_info: dict, excludes: Optional[List[str]], limit: Optional[int] = 5
) -> SimilarCompaniesResult:
    """
    根据用户查询需求和模板公司信息，搜索相似公司列表，并返回筛选后的相似公司列表

    Args:
        user_query: 用户查询
        company_info: 模板公司信息
        excludes: 排除的公司列表
        limit: 限制返回公司的数量，默认5

    Returns:
        SimilarCompaniesResult: 筛选后的相似公司列表
    """
    logging.info("开始搜索相似公司列表...")

    llm = init_model(model="gemini-2.5-pro", max_tokens=8192, temperature=0.1, include_thoughts=False)
    tools = [get_company_details, search_organizations]

    system_prompt = load_file("agent/account/prompts/search_similar_companies.md")
    prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")])

    # 创建 React Agent 并指定 response_format
    agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=prompt,
        response_format=SimilarCompaniesResult,
    )

    # 输入消息
    input_message = (
        f"用户查询需求：\n{user_query}\n\n公司信息：\n{json.dumps(company_info)}\n\n限制返回的公司数量：{limit}"  # noqa: E501
    )
    if excludes and len(excludes) > 0:
        input_message += f"\n\n需要排除的公司列表：\n{json.dumps(excludes)}"

    result = await agent.ainvoke({"messages": [("human", input_message)]})

    # 获取结构化输出
    if result.get("structured_response"):
        structured_result: SimilarCompaniesResult = result.get("structured_response")
        logging.info(f"搜索相似公司列表完成，获取到 {len(structured_result.companies)} 个候选公司")
    else:
        raise Exception("no valid search result")

    # 校验结果
    if structured_result.error:
        raise Exception(structured_result.error)
    if len(structured_result.companies) == 0:
        raise Exception("no similar companies found")

    return structured_result


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()
    # 测试数据
    user_query = "我想找一些像Cirrus Link Solutions这样的需要使用边缘网关产品的Industrial IoT公司，我想把我们的边缘网关产品卖给他们"  # noqa: E501
    company_info = {"name": "Cirrus Link Solutions", "website": "http://www.cirrus-link.com/"}
    excludes = ["Inhand Networks", "Ingersoll Rand", "Cirrus Link Solutions"]

    result: SimilarCompaniesResult = asyncio.run(search_similar_companies(user_query, company_info, excludes))
    logging.info(result.model_dump_json())
