import asyncio
import os
from pathlib import Path

import httpx
import yaml
from diskcache import Cache
from langchain_core.runnables import chain
from langchain_core.tools import tool
from loguru import logger

from utils import init_model

# 创建缓存目录和Cache对象
# 使用相对路径创建缓存目录
CACHE_DIR = Path(__file__).parent / ".cache"
CACHE_DIR.mkdir(parents=True, exist_ok=True)

# 初始化diskcache，设置缓存目录
linkedin_cache = Cache(
    directory=str(CACHE_DIR / "linkedin"),
    eviction_policy="least-recently-used",
    size_limit=int(1e9),
    cull_limit=10,
)

# 设置默认缓存期限为30天（以秒为单位）
CACHE_EXPIRY = 30 * 24 * 60 * 60  # 30天


class RapidAPIClient:
    """
    LinkedIn RapidAPI客户端类，封装所有API调用功能
    """

    def __init__(self):
        self._client: httpx.AsyncClient | None = None
        self._semaphore = asyncio.Semaphore(2)

    @property
    def client(self) -> httpx.AsyncClient:
        """
        懒加载创建HTTP客户端
        """
        if self._client is None:
            # check required environment variables
            rapid_api_host = os.environ.get("RAPID_API_HOST")
            rapid_api_key = os.environ.get("RAPID_API_KEY")

            if not rapid_api_host:
                raise ValueError("RAPID_API_HOST environment variable is required")
            if not rapid_api_key:
                raise ValueError("RAPID_API_KEY environment variable is required")

            self._client = httpx.AsyncClient(
                timeout=30.0,
                headers={
                    "x-rapidapi-host": rapid_api_host,
                    "x-rapidapi-key": rapid_api_key,
                },
                base_url=f"https://{rapid_api_host}",
            )
        return self._client

    async def get_linkedin_profile(self, profile_url: str) -> dict:
        """
        获取单个LinkedIn用户资料
        Args:
            profile_url: LinkedIn个人资料URL
        Returns:
            包含用户资料数据的字典或错误信息
        """
        async with self._semaphore:
            logger.debug(f"send request to fetch LinkedIn profile: {profile_url}")

            # 设置参数
            params = {"username": self.extract_linkedin_username(profile_url)}

            # 发送请求
            response = await self.client.get("/data-connection-count", params=params)

            # 检查请求是否成功
            if response.status_code == 200:
                logger.debug(f"successfully fetch LinkedIn profile: {profile_url}")
                result = response.json()
                if result.get("success", True):
                    data = result.get("data", {})
                    data["connections"] = result.get("connection", 0)
                    data["followers"] = result.get("follower", 0)
                    return self.simplify_linkedin_profile(data)
                else:
                    message = result.get("message", "")
                    logger.warning(f"failed to fetch LinkedIn profile: {profile_url}, {message}")
                    return {"linkedin_url": profile_url, "error": message}
            else:
                response.raise_for_status()

    async def get_company_by_domain(self, domain: str) -> dict:
        """
        根据域名获取公司信息
        Args:
            domain: 公司域名，如 apple.com
        Returns:
            包含公司信息的字典或错误信息
        """
        async with self._semaphore:
            logger.debug(f"send request to fetch company by domain: {domain}")

            # 设置参数
            params = {"domain": domain}

            # 发送请求
            response = await self.client.get("/get-company-by-domain", params=params)

            # 检查请求是否成功
            if response.status_code == 200:
                logger.debug(f"successfully fetch company by domain: {domain}")
                result = response.json()
                if result.get("success", True):
                    return self.simplify_company_data(result.get("data", {}))
                else:
                    message = result.get("message", "")
                    logger.warning(f"failed to fetch company by domain: {domain}, {message}")
                    return {"domain": domain, "error": message}
            else:
                response.raise_for_status()

    async def get_company_by_username(self, username: str) -> dict:
        """
        根据公司ID获取公司信息
        """
        async with self._semaphore:
            logger.debug(f"send request to fetch company by username: {username}")
            # 发送请求
            response = await self.client.get("/get-company-details", params={"username": username})

            # 检查请求是否成功
            if response.status_code == 200:
                logger.debug(f"successfully fetch company by username: {username}")
                result = response.json()
                if result.get("success", True):
                    return self.simplify_company_data(result.get("data", {}))
                else:
                    message = result.get("message", "")
                    logger.warning(f"failed to fetch company by username: {username}, {message}")
                    return {"username": username, "error": message}
            else:
                response.raise_for_status()

    @staticmethod
    def extract_linkedin_username(profile_url: str) -> str:
        """
        从LinkedIn个人资料URL中提取用户名

        Args:
            profile_url: LinkedIn个人资料URL，如 https://www.linkedin.com/in/username 或 http://www.linkedin.com/in/username/

        Returns:
            提取出的用户名
        """
        # 处理空URL情况
        if not profile_url:
            logger.info("empty LinkedIn profile URL provided")
            return ""

        # 如果URL包含问号，去掉问号及其后面的内容
        if "?" in profile_url:
            profile_url = profile_url.split("?")[0]

        # 如果URL以斜杠结尾，去掉结尾的斜杠
        if profile_url.endswith("/"):
            profile_url = profile_url[:-1]

        # 提取/in/后面的部分作为用户名
        if "/in/" in profile_url:
            username = profile_url.split("/in/")[-1]
            return username

        # 如果URL格式不符合预期，返回原始URL
        logger.warning(f"could not extract LinkedIn username from URL: {profile_url}")
        return profile_url

    @staticmethod
    def simplify_linkedin_profile(profile_data: dict) -> dict:
        """
        Simplify the LinkedIn profile data to the fields needed for candidate analysis.
        """
        try:
            simplified_data = {
                "name": f"{profile_data.get('firstName', '')} {profile_data.get('lastName', '')}",
                "headline": profile_data.get("headline", ""),
                "location": profile_data.get("geo", {}).get("full", ""),
                "connections": profile_data.get("connections", 0),
                "followers": profile_data.get("followers", 0),
                "summary": profile_data.get("summary", ""),
                "current_position": None,
                "experience": [],
                "education": [],
                "skills": "",
            }

            # extract current position
            if "position" in profile_data and profile_data["position"]:
                current_position = profile_data["position"][0]
                simplified_data["current_position"] = {
                    "title": current_position.get("title", ""),
                    "company": current_position.get("multiLocaleCompanyName", {}).get("en_US", ""),
                    "description": current_position.get("description", ""),
                }

            # extract top skills
            if "skills" in profile_data and profile_data["skills"]:
                simplified_data["skills"] = ", ".join([skill.get("name", "") for skill in profile_data["skills"]])
            return simplified_data
        except Exception as e:
            logger.warning(f"Error simplifying profile data: {str(e)}")
            return profile_data

    @staticmethod
    def simplify_company_data(data: dict) -> dict:
        """
        Simplify the company data to the fields needed for analysis.
        """
        try:
            simplified_data = {
                "id": data.get("id", ""),
                "name": data.get("name", ""),
                "universal_name": data.get("universalName", ""),
                "linkedin_url": data.get("linkedinUrl", ""),
                "description": data.get("description", ""),
                "tagline": data.get("tagline", ""),
                "type": data.get("type", ""),
                "website": data.get("website", ""),
                "staff_count": data.get("staffCount", 0),
                "staff_count_range": data.get("staffCountRange", ""),
                "follower_count": data.get("followerCount", 0),
                "founded": data.get("founded"),
                "industries": data.get("industries", []),
                "specialities": data.get("specialities", []),
            }

            # extract headquarters
            if "headquarter" in data and data["headquarter"]:
                hq = data["headquarter"]
                simplified_data["headquarters"] = {
                    "city": hq.get("city", ""),
                    "country": hq.get("country", ""),
                    "geographic_area": hq.get("geographicArea", ""),
                    "postal_code": hq.get("postalCode", ""),
                    "line1": hq.get("line1", ""),
                }

            return simplified_data
        except Exception as e:
            logger.warning(f"Error simplifying company data: {str(e)}")
            return data

    async def close(self):
        """
        关闭HTTP客户端连接
        """
        if self._client is not None:
            await self._client.aclose()
            self._client = None

    async def get_company_posts(self, username: str, start: int = 0) -> dict:
        """
        获取公司的LinkedIn posts
        Args:
            username: 公司用户名
            start: 起始位置，默认为0
        Returns:
            包含公司posts数据的字典或错误信息
        """
        async with self._semaphore:
            logger.debug(f"send request to fetch company posts: {username}")

            # 设置参数
            params = {"username": username, "start": start}

            # 发送请求
            response = await self.client.get("/get-company-posts", params=params)

            # 检查请求是否成功
            if response.status_code == 200:
                logger.debug(f"successfully fetch company posts: {username}")
                result = response.json()
                if result.get("success", True):
                    data = result.get("data", [])
                    return {
                        "username": username,
                        "total": result.get("total", 0),
                        "total_page": result.get("totalPage", 0),
                        "pagination_token": result.get("paginationToken", ""),
                        "posts": data.get("posts", []) if isinstance(data, dict) else data,
                    }
                else:
                    message = result.get("message", "")
                    logger.warning(f"failed to fetch company posts: {username}, {message}")
                    return {"username": username, "error": message}
            else:
                response.raise_for_status()


# 全局API客户端实例
rapidapi = RapidAPIClient()


@chain
async def _get_linkedin_profile(profile_url: str) -> dict:
    """
    Get the LinkedIn profile of a given URL.
    """
    logger.debug(f"获取LinkedIn资料: {profile_url}")
    profile_data = linkedin_cache.get(profile_url)
    if profile_data is None:
        profile_data = await rapidapi.get_linkedin_profile(profile_url)
        if "error" not in profile_data:
            linkedin_cache.set(profile_url, profile_data)
        else:
            logger.warning(f"获取LinkedIn资料失败: {profile_data['error']}")
    return profile_data


@tool
async def get_linkedin_profiles(explanation: str, profile_urls: list[str]) -> str:
    """
    获取多个LinkedIn用户的详细资料, 此工具不要并发使用。
    Args:
        explanation: One sentence explanation as to why this tool is being used, and how it contributes to the goal.
        profile_urls: LinkedIn个人资料URL列表 (例如 ["https://www.linkedin.com/in/username1/", "https://www.linkedin.com/in/username2/"])
    Returns:
        包含所有用户LinkedIn资料的JSON字符串
    """  # noqa: E501
    results = await _get_linkedin_profile.abatch(profile_urls, config={"max_concurrency": 2})
    return yaml.dump(results, indent=2)


@chain
async def _get_company_by_domain(domain: str | None = None) -> dict:
    """
    Get the company information by domain.
    """
    logger.debug(f"获取公司信息: {domain}")
    company_data = linkedin_cache.get(domain)
    if company_data is None:
        company_data = await rapidapi.get_company_by_domain(domain)
        if "error" in company_data:
            logger.warning(f"获取公司信息失败: {company_data['error']}")
        else:
            linkedin_cache.set(domain, company_data)
    return company_data


@chain
async def _get_company_by_username(username: str) -> dict:
    """
    Get the company information by username.
    """
    logger.debug(f"获取公司信息: {username}")
    company_data = linkedin_cache.get(username)
    if company_data is None:
        company_data = await rapidapi.get_company_by_username(username)
        if "error" in company_data:
            logger.warning(f"获取公司信息失败: {company_data['error']}")
        else:
            linkedin_cache.set(username, company_data)
    return company_data


@tool
async def get_company_details(
    explanation: str, domains: list[str] | None = None, usernames: list[str] | None = None
) -> str:
    """
    根据域名获取多个公司的详细信息, 此工具不要并发使用。
    Args:
        explanation: One sentence explanation as to why this tool is being used, and how it contributes to the goal.
        domains: 公司域名列表 (例如 ["apple.com", "google.com", "microsoft.com"])
        usernames: 公司用户名列表 (例如 ["apple", "google", "microsoft"])
    Returns:
        包含所有公司信息的JSON字符串
    """  # noqa: E501
    results = []
    if domains:
        results.extend(await _get_company_by_domain.abatch(domains, config={"max_concurrency": 2}))

    if usernames:
        results.extend(await _get_company_by_username.abatch(usernames, config={"max_concurrency": 2}))

    return yaml.dump(results, indent=2)


@tool
async def get_company_posts(explanation: str, username: str = None, summarize: bool = True) -> str:
    """
    Get LinkedIn posts for a company and provide AI summarization analysis.
    Args:
        explanation: One sentence explanation as to why this tool is being used, and how it contributes to the goal.
        username: Company username (e.g. "google", "microsoft", "apple")
        summarize: Whether to provide AI summary of posts, default is True
    Returns:
        JSON string containing company posts data, with AI analysis if summarization is enabled
    """  # noqa: E501
    # get company posts with caching
    logger.debug(f"获取公司posts: {username}")
    cache_key = f"posts_{username}"
    result = linkedin_cache.get(cache_key)

    if result is None:
        result = await rapidapi.get_company_posts(username, 0)
        if "error" not in result:
            # cache for 24 hours
            linkedin_cache.set(cache_key, result, expire=24 * 60 * 60)
        else:
            logger.warning(f"获取公司posts失败: {result['error']}")
            return yaml.dump(result, indent=2)

    # if summarization is requested, analyze the posts using AI
    if summarize and "posts" in result and result["posts"]:
        try:
            # initialize gpt-4.1-nano model for summarization
            llm = init_model(
                model="gpt-4.1-nano",
                max_tokens=2048,
                temperature=0.5,
            )

            # prepare posts content for analysis
            posts_content = []
            company_name = result.get("username", username)
            for post in result["posts"][:25]:  # limit to top 25 posts
                post_text = post.get("text", "")
                if post_text:
                    posts_content.append(f"Company: {company_name}\nPost: {post_text}\n")

            if posts_content:
                # create summarization prompt
                prompt = f"""Please analyze the following LinkedIn company posts and provide a comprehensive summary:

{chr(10).join(posts_content)}

Please provide:
1. Key themes and topics discussed
2. Company positioning and messaging strategies
3. Industry trends mentioned
4. Notable announcements or updates
5. Others (if any)

Respond in Chinese. Respond without any explanation."""

                summary = await llm.ainvoke(prompt)

                # add summary to result
                return summary.content if hasattr(summary, "content") else str(summary)

        except Exception as e:
            logger.warning(f"posts总结失败: {str(e)}")
            # add error info but continue with raw data
            return f"summary error: {str(object=e)}"

    return yaml.dump(result, indent=2)


if __name__ == "__main__":
    import dotenv

    dotenv.load_dotenv()

    async def main() -> None:
        # test company posts
        posts_data = await get_company_posts.ainvoke(
            input={
                "explanation": "test company posts lookup and summarization",
                "username": "thinaer",
                "summarize": True,
            }
        )
        print("\nCompany Posts:")
        print(posts_data)
        return

        # test LinkedIn profiles
        # profile_data = await get_linkedin_profiles.ainvoke(
        #     input={
        #         "explanation": "test",
        #         "profile_urls": [
        #             "http://www.linkedin.com/in/username1",
        #             # "http://www.linkedin.com/in/username2/",
        #             # "http://www.linkedin.com/in/username3/",
        #         ],
        #     }
        # )
        # print("LinkedIn Profiles:")
        # print(profile_data)

        # test company by domain
        company_data = await get_company_details.ainvoke(
            input={
                "explanation": "test company lookup",
                "domains": ["www.thinaer.io"],
                "usernames": ["thinaer"],
            }
        )
        print("\nCompany Data:")
        print(company_data)

    asyncio.run(main())
