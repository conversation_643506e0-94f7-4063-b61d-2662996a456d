import os
from typing import TypedDict

from langchain.tools import tool
from langchain.tools.base import ToolException
from langchain_core.output_parsers import StrOutputParser
from langchain_tavily.tavily_crawl import TavilyCrawl
from langchain_tavily.tavily_search import <PERSON>lySearch
from loguru import logger

from utils import init_model


@tool
async def tavily_crawl(
    explanation: str,
    url: str,
    instructions: str | None = None,
) -> str:
    """
    A powerful web crawler that initiates a structured web crawl starting from a specified
    base URL. The crawler uses a BFS Depth: refering to the number of link hops from the root URL.
    A page directly linked from the root is at BFS depth 1, regardless of its URL structure.
    You can control how deep and wide it goes, and guide it to focus on specific sections of the site.

    Args:
        explanation: One sentence explanation why this tool is used and how it contributes to the goal in first person.
        url: the target URL to crawl
        instructions: optional natural language instructions to guide the crawler

    Returns:
        summarized content from the crawled pages
    """
    try:
        # create TavilyCrawl instance with essential parameters only
        crawl_tool = TavilyCrawl(
            max_depth=3,  # stay local to the base URL
            max_breadth=10,  # reasonable number of links per page
            limit=20,  # total number of pages to process
            extract_depth="basic",  # faster extraction
            include_images=False,  # reduce payload size
        )

        # execute crawl with parameters
        crawl_params = {
            "url": url,
        }

        # add instructions if provided
        if instructions:
            crawl_params["instructions"] = instructions

        raw_results = await crawl_tool.ainvoke(input=crawl_params)

        # check if results exist
        if not raw_results or not raw_results.get("results"):
            return f"No content found for URL: {url}"

        # extract content from results
        content_parts = []
        for result in raw_results.get("results", []):
            content_parts.append(f"URL: {result['url']}\nContent:\n{result['raw_content']}")

        # combine all content
        combined_content = "\n\n---\n\n".join(content_parts)

        # initialize gpt-4.1-nano model for summarization
        llm = init_model(
            model="gpt-4.1-mini",
            max_tokens=2048,
            temperature=0,
        )

        # create summarization prompt
        # create unified professional analysis prompt
        summary_prompt = f"""作为专业分析师，请基于以下爬取的网页内容进行回答用户问题。

源URL: {url}
{f"用户问题: {instructions}" if instructions else "总结所有内容，提取关键信息"}

网页内容:
{combined_content}

回答需要标明数据真实来源URL，不要使用虚假的链接
请确保回答准确、详细、专业且具有实用价值。用中文回复。
仅回答用户问题，不要解释和输出任何其他内容。"""

        # get summary from model
        chain = llm | StrOutputParser()
        summary = await chain.ainvoke(summary_prompt)
        return summary

    except Exception as e:
        logger.error(f"Error in tavily_crawl tool: {e}")
        return f"Error crawling URL {url}: {str(e)}"


# tavily search result type definitions
class TavilySearchResult(TypedDict):
    """single search result from tavily"""

    title: str
    url: str
    content: str
    score: float
    raw_content: str | None


class TavilySearchResponse(TypedDict):
    """response structure from tavily_search tool"""

    query: str
    follow_up_questions: list[str] | None
    answer: str | None
    images: list[str]
    results: list[TavilySearchResult]
    response_time: float


async def _mock_tavily_search(query: str, include_domains: list[str] = []) -> TavilySearchResponse:
    try:
        llm = init_model(
            model="gpt-4.1-nano",
            max_tokens=1024,
            temperature=0.7,
        )

        prompt = f"""Generate a realistic mock search response for the query: "{query}"
        params: `include_domains` is {include_domains}
        Make sure the content is relevant to the query and realistic. Generate 3-5 results.

        result score field: 0-1 float, the score of the result

        Only return the JSON object, no other text."""

        response = await llm.with_structured_output(TavilySearchResponse).ainvoke(prompt)
        return response
    except Exception as e:
        logger.error(f"Error generating mock response: {e}")
        # fallback to basic response
        return {
            "query": query,
            "follow_up_questions": [],
            "answer": f"Search results for: {query}",
            "images": [],
            "results": [],
            "response_time": 0.1,
        }


@tool(parse_docstring=True)
async def tavily_search(
    query: str,
    include_domains: list[str] | None = None,
    include_raw_content: bool = False,
) -> TavilySearchResponse:
    """
    A search engine optimized for comprehensive, accurate, and trusted results.
    Useful for when you need to answer questions about current events.
    It not only retrieves URLs and snippets, but offers advanced search depths,
    domain management, time range filters, and image search, this tool delivers
    real-time, accurate, and citation-backed results.
    Input should be a search query.

    Args:
        query: search query string
        include_domains: optional list of domains to restrict search to
        include_raw_content: optional boolean to include raw content in the response

    Returns:
        processed search results as formatted string
    """
    if include_domains is None:
        include_domains = []

    if os.getenv("MOCK_TAVILY_SEARCH"):
        # use gpt-4.1-nano to generate mock search results
        return await _mock_tavily_search(query, include_domains)

    # create TavilySearch instance
    tavily_tool = TavilySearch(
        max_results=5,
        include_answer="basic",
        include_raw_content=include_raw_content,
        search_depth="basic",
        include_images=False,
    )

    # execute search with parameters
    response = await tavily_tool._arun(query=query, include_domains=include_domains or [])

    if "error" in response:
        raise ToolException(f"Error in tavily_search tool: {response['error']}")

    # filter results by score threshold
    filtered_results = [result for result in response.get("results", []) if result.get("score", 0) > 0.3]

    response["results"] = filtered_results

    return response


async def summary_tavily_search_results(query: str, search_response: TavilySearchResponse) -> str:
    """
    Analyze TavilySearchResponse using LLM based on query

    Args:
        query: the original search query
        search_response: TavilySearchResponse object containing search results

    Returns:
        analyzed and summarized content based on the query
    """
    try:
        # prepare search content for analysis
        search_content = f"Search Query: {search_response['query']}\n\n"

        # add tavily's answer if available
        if search_response.get("answer"):
            search_content += f"Tavily Answer: {search_response['answer']}\n\n"

        # add search results
        search_content += "Search Results:\n"
        for i, result in enumerate(search_response.get("results", []), 1):
            search_content += f"\n{i}. Title: {result['title']}\n"
            search_content += f"   URL: {result['url']}\n"
            search_content += f"   Score: {result['score']}\n"
            search_content += f"   Content: {result['content']}\n"
            if result.get("raw_content"):
                search_content += f"   Raw Content: {result['raw_content']}\n"

        # add follow up questions if available
        if search_response.get("follow_up_questions"):
            search_content += "\nFollow-up Questions:\n"
            for question in search_response["follow_up_questions"]:
                search_content += f"- {question}\n"

        # use LLM to analyze and summarize based on query
        analysis_llm = init_model(model="gpt-4.1-mini", max_tokens=4096, temperature=0)

        analysis_prompt = f"""
请根据以下查询需求，分析并总结提供的搜索结果：

## 要求
- 请提供相关的分析和总结，重点关注与查询需求相关的信息
- 确保回答完整、明确、明了，突出关键信息
- 直接回复结果，不要添加任何解释和总结
- 只针对查询需求中提到的内容进行回答，删除多余的完全无用搜索内容
- 分析查询需求，保留与查询需求可能相关的搜索内容
- 只从搜索结果中抽取与查询需求相关的内容，不要添加你自己的分析和总结
- Always respond in Chinese

<查询需求>
{query}
</查询需求>

<搜索结果>
{search_content}
</搜索结果>

## 输出要求
- Keep the links in the content for further research.
- Always provide a citation for the source.
- Use Reference Links citations for everything using markdown link notation with the url provided.
- Define Reference Links at the end of the document.
- All facts must be cited.
"""

        # get analysis from LLM
        summary = await analysis_llm.ainvoke(analysis_prompt)
        analyzed_content = summary.content if hasattr(summary, "content") else str(summary)

        # collect all URLs from search results
        all_urls = [result["url"] for result in search_response.get("results", [])]
        if all_urls:
            return analyzed_content + "\n\nReference URLs:\n" + "\n".join(all_urls)
        return analyzed_content

    except Exception as e:
        logger.error(f"Error analyzing tavily search results: {e}")
        return f"Error analyzing search results: {str(e)}"


if __name__ == "__main__":
    import asyncio

    from dotenv import load_dotenv

    load_dotenv()
    load_dotenv(".env.local", override=True)

    async def main():
        result = await tavily_crawl.ainvoke(
            input={
                "url": "https://thinaer.io/partners/",
                "instructions": "thinaer 的硬件合作伙伴都有哪些，以及分别与他们的合作方式",
            }
        )
        print("result:\n", result)

    asyncio.run(main())
