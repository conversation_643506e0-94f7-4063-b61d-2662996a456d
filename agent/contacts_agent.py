from typing import Optional

from langchain_core.messages import AIMessage, HumanMessage, ToolMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import (
    ChatPromptTemplate,
)
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.contacts_agent_state import ContactsAgentState
from agent.filter_people_tools import filter_people, think_people_search_strategy
from agent.search_apollo_tools import search_apollo_people, search_organization
from agent.search_linkedin_tools import get_linkedin_profiles
from agent.tools import tavily_search, think
from config import CONTACTS_AGENT_MODEL, CONTACTS_RECURSION_LIMIT
from utils.file_handler import load_file
from utils.models import init_model

# Maximum number of times a tool can fail with the same error consecutively before intervention.
MAX_CONSECUTIVE_TOOL_ERRORS = 6


class ContactsAgent:
    """
    联系人搜索代理
    """

    def __init__(
        self,
        exclude_linkedin_urls: Optional[list[str]] = None,
    ):
        if exclude_linkedin_urls is None:
            exclude_linkedin_urls = []

        self._initial_state = {
            "exclude_linkedin_urls": exclude_linkedin_urls,
            "search_strategy": "",
        }
        # Add a tracker for consecutive errors. This is necessary for the hook's logic.
        self._consecutive_error_tracker = {"tool_name": None, "error_content_hash": None, "count": 0}

        # CONTACTS_AGENT_MODEL = "gpt-4.1"
        self.llm = init_model(model=CONTACTS_AGENT_MODEL, temperature=0.1, max_tokens=2048)
        logger.info(f"Initializing ContactsAgent with model: {CONTACTS_AGENT_MODEL}")
        self._init_agent()

    def _reset_error_tracker(self):
        """Resets the consecutive tool error counter."""
        self._consecutive_error_tracker = {"tool_name": None, "error_content_hash": None, "count": 0}

    def _create_intervention_messages(self, tool_name: str, tool_id: str) -> dict:
        """Creates the messages to intervene when a tool is stuck."""
        fake_tool_message = ToolMessage(
            content=(
                f"Error: Tool '{tool_name}' was not executed because it has failed "
                f"consecutively more than {MAX_CONSECUTIVE_TOOL_ERRORS} times."
            ),
            tool_call_id=tool_id,
        )
        intervention_message = HumanMessage(
            content=(
                f"SYSTEM: The tool '{tool_name}' was blocked from running because it failed "
                f"{MAX_CONSECUTIVE_TOOL_ERRORS} consecutive times with the same error. "
                "Please analyze the root cause of the persistent error and try a different "
                "approach, such as using a different tool or modifying the arguments."
            )
        )
        return {"messages": [fake_tool_message, intervention_message]}

    def _post_model_hook(self, state: ContactsAgentState) -> dict:
        """Hook to intervene if a tool fails with the same error consecutively."""
        messages = state["messages"]
        history = messages[:-1]  # History before the current model decision

        # Part 1: Update error counter based on the last tool result in history.
        # Find the last ToolMessage to see what happened.
        last_tool_idx = -1
        for i, msg in reversed(list(enumerate(history))):
            if isinstance(msg, ToolMessage):
                last_tool_idx = i
                break

        if last_tool_idx < 1:
            self._reset_error_tracker()
        else:
            last_tool_result = history[last_tool_idx]
            prev_model_decision = history[last_tool_idx - 1]

            if isinstance(prev_model_decision, AIMessage) and prev_model_decision.tool_calls:
                prev_tool_call = prev_model_decision.tool_calls[0]
                prev_tool_name = prev_tool_call.get("name") if isinstance(prev_tool_call, dict) else prev_tool_call.name
                tool_output = last_tool_result.content
                # Ensure tool_output is a string before calling .lower() to avoid type errors.
                tool_output_str = str(tool_output)
                is_error = "error" in tool_output_str.lower() or "exception" in tool_output_str.lower()

                if is_error:
                    error_hash = hash(tool_output_str)
                    # If same tool and same error, increment count
                    if (
                        self._consecutive_error_tracker["tool_name"] == prev_tool_name
                        and self._consecutive_error_tracker["error_content_hash"] == error_hash
                    ):
                        self._consecutive_error_tracker["count"] += 1
                    else:  # New error or new tool, reset and start count at 1
                        self._consecutive_error_tracker = {
                            "tool_name": prev_tool_name,
                            "error_content_hash": error_hash,
                            "count": 1,
                        }
                    logger.info(
                        f"Consecutive error for tool '{prev_tool_name}': "
                        f"count = {self._consecutive_error_tracker['count']}."
                    )
                else:  # Success breaks the chain
                    self._reset_error_tracker()
            else:  # Message sequence isn't what we expect, reset.
                self._reset_error_tracker()

        # Part 2: Check if the current model decision should be blocked.
        current_model_decision = messages[-1]
        if isinstance(current_model_decision, AIMessage) and current_model_decision.tool_calls:
            current_tool_call = current_model_decision.tool_calls[0]
            current_tool_name = (
                current_tool_call.get("name") if isinstance(current_tool_call, dict) else current_tool_call.name
            )

            # Check if we are about to call the tool that has been failing
            if (
                self._consecutive_error_tracker["tool_name"] == current_tool_name
                and self._consecutive_error_tracker["count"] >= MAX_CONSECUTIVE_TOOL_ERRORS
            ):
                logger.warning(
                    (
                        f"Tool '{current_tool_name}' has failed {self._consecutive_error_tracker['count']} times. "
                        "Intervening."
                    )
                )
                current_tool_id = (
                    current_tool_call.get("id") if isinstance(current_tool_call, dict) else current_tool_call.id
                )
                if current_tool_name and current_tool_id:
                    intervention = self._create_intervention_messages(current_tool_name, current_tool_id)
                    self._reset_error_tracker()  # Reset after intervening
                    return intervention
                else:
                    logger.error("Could not intervene due to missing tool name or ID.")

        return {}

    def _init_agent(self):
        tools = [
            search_organization,
            tavily_search,
            think_people_search_strategy,
            search_apollo_people,
            filter_people,
            get_linkedin_profiles,
            think,
        ]
        prompt = ChatPromptTemplate.from_messages(
            [("system", self._get_system_prompt()), ("placeholder", "{messages}")]
        )
        # model = self.llm.bind_tools(tools, parallel_tool_calls=False)
        model = self.llm.bind_tools(tools)
        self.agent = create_react_agent(
            model=model,
            tools=tools,
            prompt=prompt,
            state_schema=ContactsAgentState,
            name="contacts_agent",
            post_model_hook=self._post_model_hook,
        )

    def _get_system_prompt(self):
        return load_file("prompts/detect_contacts_prompt.md")

    def _prepare_input(self, user_query: str, organization_id: str) -> dict:
        return {
            "messages": [
                f"用户要求：{user_query}",
                f"组织 ID: {organization_id}",
                "请找出合适的潜在联系人",
            ],
            **self._initial_state,
        }

    async def ainvoke(self, user_query: str, organization_id: str) -> str:
        input_data = self._prepare_input(user_query, organization_id)
        result = await self.agent.ainvoke(
            input=input_data,
            config={"recursion_limit": CONTACTS_RECURSION_LIMIT},
        )
        return result["messages"][-1].text()

    async def get_contacts(self, user_query: str, organization_id: str) -> list[dict]:
        result = await self.ainvoke(user_query=user_query, organization_id=organization_id)
        try:
            return JsonOutputParser().parse(result)
        except Exception as e:
            logger.opt(exception=e).error(f"parse contacts result error: {result}")
            return []
