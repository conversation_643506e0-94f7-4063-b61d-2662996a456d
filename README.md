# 销售助手

## 项目概述

本项目是一个面向工业物联网产品代理商的数据分析助手，旨在帮助用户通过自动化工具和 OpenAI 的 GPT-4 模型从客户数据中提取关键信息。系统集成了多个平台（包括 Apollo、LinkedIn 和 Rapid）来收集和分析客户联系信息，并支持与 Zoho CRM 系统的数据交互。

## 主要功能

-   从多个平台（Apollo、LinkedIn、Rapid）自动收集联系人信息
-   基于公司和地理位置智能筛选潜在联系人
-   CSV 导出功能，方便联系人管理
-   集成 OpenAI 的 GPT-4 进行数据分析
-   支持无头浏览器自动化
-   SSE（服务器发送事件）服务，实现实时更新
-   支持本地和生产环境切换
-   Zoho CRM 数据集成与转换
-   潜在客户研究与挖掘功能

## 环境要求

-   Python 3.12 或更高版本
-   现代网页浏览器（用于 Playwright）
-   集成服务的 API 密钥（Apollo、LinkedIn、Rapid、OpenAI）
-   Node.js 和 pnpm（用于前端开发）

## 安装说明

### 1. 安装依赖

```sh
# 安装 uv 包管理器
pip install uv

# 安装项目依赖
uv pip install -r pyproject.toml

# 安装 Playwright 浏览器
playwright install
playwright install-deps
```

### 2. 环境配置

在项目根目录创建 `.env` 文件，包含以下变量：

```
# API密钥
OPENAI_API_KEY=你的_openai_api密钥
RAPID_API_KEY=你的_rapid_api密钥
RAPID_API_HOST=你的_rapid_api_host
RAPID_API_URL=你的_rapid_api_url

# Langfuse配置（可选）
LANGFUSE_ENABLE=false
LANGFUSE_SECRET_KEY=你的_langfuse_secret_key
LANGFUSE_PUBLIC_KEY=你的_langfuse_public_key
LANGFUSE_HOST=你的_langfuse_host

# 日志配置
LOG_LEVEL=INFO  # 可选，日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL

# 服务器配置
SERVER_HOST=0.0.0.0  # 服务器监听地址
SERVER_PORT=3000     # 服务器监听端口

# 环境配置
ENVIRONMENT=local    # 环境类型：local 或 production

# 代理配置（如需要）
HTTPS_PROXY=http://your.proxy:port
```

### 3. 前端依赖安装

```sh
cd server/html
pnpm install
```

## 使用说明

### 启动 SSE 服务

启动服务器发送事件服务：

```sh
python run_sse.py
```

服务将在 `localhost:3000` 上运行（或根据 SERVER_HOST 和 SERVER_PORT 环境变量配置的地址）

可以通过以下方式访问服务：

-   打开 `http://localhost:3000` 查看基本测试页面
-   打开 `http://localhost:3000/client` 查看带有环境切换功能的客户端测试页面

### 运行主应用程序

```sh
python main.py
```

应用程序将执行以下步骤：

1. 在指定的公司和地区搜索联系人
2. 根据预定义条件筛选潜在联系人
3. 从各个平台收集额外信息
4. 生成包含最终联系人列表的 CSV 文件
5. 将 CSV 上传至 Apollo 进行邮箱信息补充

### 开发前端

```sh
cd server/html
pnpm start
```

## 项目结构

-   `apollo/` - Apollo 平台集成
-   `linkedin/` - LinkedIn 平台集成
-   `rapid/` - Rapid 平台集成
-   `zoho/` - Zoho CRM 相关转换
-   `research/` - 潜在客户研究与挖掘功能
-   `config.py` - 项目配置文件（环境变量、系统参数等）
-   `run_sse.py` - SSE 服务启动脚本
-   `server/` - 服务器组件（包括 SSE 服务）
-   `utils/` - 工具函数
-   `prompts/` - GPT 提示词和模板
-   `output/` - 生成的输出文件
-   `logs/` - 日志文件目录

## 开发指南

-   项目使用 pre-commit hooks 进行代码质量控制
-   使用 Ruff 0.11.4 进行代码检查和格式化
-   建议使用类型提示以提高代码可维护性
-   日志输出包含 sessionId，便于跟踪和查询特定会话的日志
-   日志输出中的中文、英文和数字之间应有空格（如 '总共有 8 个 worker 的'）
-   日志时间戳使用服务器时间和时区
-   任务队列管理采用异步锁而非线程锁，提高并发性能

## 环境切换

系统支持在本地环境和生产环境之间切换：

-   本地环境（Local）：使用 127.0.0.1 作为主机地址
-   生产环境（Production）：使用配置的生产服务器地址

可以通过客户端页面上的环境切换开关进行切换，系统会记住您的选择。

## 配置参数

以下是系统的配置参数，可以通过环境变量进行覆盖：

### API 配置

| 参数名称       | 默认值        | 说明               |
| -------------- | ------------- | ------------------ |
| OPENAI_API_KEY | "sk-proj-..." | OpenAI API 密钥    |
| MODEL          | "gpt-4.1"     | 使用的 OpenAI 模型 |

### 代理配置

| 参数名称    | 默认值                   | 说明          |
| ----------- | ------------------------ | ------------- |
| HTTPS_PROXY | "http://10.5.17.45:8118" | HTTP 代理地址 |

### Rapid API 配置

| 参数名称       | 默认值                                                       | 说明               |
| -------------- | ------------------------------------------------------------ | ------------------ |
| RAPID_ENABLE   | false                                                        | 是否启用 Rapid API |
| RAPID_API_URL  | "https://linkedin-api8.p.rapidapi.com/data-connection-count" | Rapid API 的 URL   |
| RAPID_API_KEY  | "3fb1bc64c0msh..."                                           | Rapid API 密钥     |
| RAPID_API_HOST | "linkedin-api8.p.rapidapi.com"                               | Rapid API 的主机名 |

### Langfuse 配置

| 参数名称            | 默认值                           | 说明              |
| ------------------- | -------------------------------- | ----------------- |
| LANGFUSE_ENABLE     | false                            | 是否启用 Langfuse |
| LANGFUSE_SECRET_KEY | "sk-lf-5737802e-..."             | Langfuse 秘钥     |
| LANGFUSE_PUBLIC_KEY | "pk-lf-82553b1e-..."             | Langfuse 公钥     |
| LANGFUSE_HOST       | "https://langfuse.inhand.online" | Langfuse 主机地址 |

### 环境配置

| 参数名称    | 默认值  | 说明                            |
| ----------- | ------- | ------------------------------- |
| ENVIRONMENT | "local" | 环境类型（local 或 production） |

### 服务器配置

| 参数名称    | 默认值    | 说明                   |
| ----------- | --------- | ---------------------- |
| SERVER_HOST | "0.0.0.0" | 服务器监听地址         |
| SERVER_PORT | 3000      | 服务器监听端口         |
| SSE_TIMEOUT | 120       | SSE 连接超时时间（秒） |

### 并发和容量配置

| 参数名称                | 默认值 | 说明                 |
| ----------------------- | ------ | -------------------- |
| MAX_CONCURRENT_SESSIONS | 30     | 最大并发会话数       |
| MAX_BROWSER_PAGES       | 20     | 最大浏览器页面数     |
| MAX_CONCURRENT_TASKS    | 20     | 最大并发任务数       |
| MAX_MESSAGE_QUEUE_SIZE  | 100    | 消息队列最大容量     |
| MAX_WORKERS             | 30     | 最大 Worker 数量     |
| MAX_IDLE_WORKERS        | 5      | 最大空闲 Worker 数量 |

### 时间和间隔配置

| 参数名称                     | 默认值 | 说明                         |
| ---------------------------- | ------ | ---------------------------- |
| TASK_CLEANUP_INTERVAL        | 3600   | 任务清理间隔（秒）           |
| WORKER_HEALTH_CHECK_INTERVAL | 300    | Worker 健康检查间隔（秒）    |
| WORKER_IDLE_CLEANUP_INTERVAL | 1800   | 空闲 Worker 清理间隔（秒）   |
| TASK_MONITOR_INTERVAL        | 60     | 任务监控检查间隔（秒）       |
| MAX_TASK_RUNTIME             | 300    | 任务最大运行时间（秒）       |
| TEMP_FILE_CLEANUP_INTERVAL   | 3600   | 临时文件清理间隔（秒）       |
| TEMP_FILE_MAX_AGE            | 24     | 临时文件最大保留时间（小时） |
| MAX_SESSION_IDLE_TIME        | 3600   | 会话最大不活动时间（秒）     |

### 日志配置

| 参数名称     | 默认值 | 说明                                              |
| ------------ | ------ | ------------------------------------------------- |
| LOG_LEVEL    | "INFO" | 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL） |
| MAX_LOG_DAYS | 3      | 日志保留天数                                      |
