[project]
name = "sales-agent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.11.14",
    "colorama>=0.4.6",
    "colorlog>=6.8.2",
    "diskcache>=5.6.3",
    "fastapi[standard]>=0.115.12",
    "httpx>=0.28.1",
    "ipykernel>=6.29.5",
    "ipython<9.0.0",
    "langchain>=0.3.19",
    "langchain-openai>=0.3.7",
    "langfuse>=2.59.6",
    "langgraph>=0.3.31",
    "mcp-use>=1.2.7",
    "openai>=1.64.0",
    "openpyxl>=3.1.5",
    "pandas>=2.2.3",
    "playwright>=1.50.0",
    "pre-commit>=4.1.0",
    "python-dotenv>=1.0.1",
    "requests>=2.32.3",
    "rich>=13.9.4",
    "typing-extensions>=4.13.0",
    "uvicorn>=0.34.0",
    "pymongo>=4.6.2",
    "langgraph-cli[inmem]>=0.2.8",
    "langchain-tavily>=0.1.6",
    "langchain-aws>=0.2.23",
    "ftfy>=6.2.0",
    "orjson>=3.10.16",
    "taskiq>=0.11.17",
    "taskiq-redis>=1.0.8",
    "msgpack>=1.1.0",
    "loguru>=0.7.3",
    "pypubsub>=4.0.3",
    "langchain-google-genai>=2.1.4",
    "motor>=3.7.1",
    "pydantic>=2.11.4",
    "langmem>=0.0.27",
    "firecrawl-py>=2.7.1",
    "langgraph-supervisor>=0.0.27",
    "python-consul>=1.1.0",
]

[dependency-groups]
dev = ["ruff>=0.11.4", "pytest>=8.4.0", "pytest-asyncio>=1.0.0"]

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = "tests"
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = "-v --tb=short -s -W ignore"
