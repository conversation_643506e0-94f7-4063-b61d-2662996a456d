from typing import Any, Dict, List

from utils.file_handler import load_json_file
from utils.logger import get_logger
from zoho.summarize_info import summarize_apollo_user_info

logging = get_logger(__name__)

# 部门列表
departments_list = load_json_file("zoho/departments.json")


def get_twitter_name(twitter_url: str) -> str:
    # Handle None, empty strings, and NaN values
    if not twitter_url:
        return ""

    # Check for NaN as string
    if isinstance(twitter_url, str) and twitter_url.lower() == "nan":
        return ""

    # Check for NaN as float
    if isinstance(twitter_url, float) and str(twitter_url).lower() == "nan":
        return ""

    # Convert to string if not already
    twitter_url = str(twitter_url)

    # 如果 twitter_url 包含 https://twitter.com/xxxyyy 则返回 xxxyyy
    if "https://twitter.com/" in twitter_url:
        return twitter_url.split("/")[-1]
    # 如果 twitter_url 包含 https://x.com/xxxyyy 则返回 xxxyyy
    elif "https://x.com/" in twitter_url:
        return twitter_url.split("/")[-1]
    return ""


# 确保所有值都是字符串类型，如果是 None、NaN、不存在或空值则返回空字符串
def safe_str(value: Any) -> str:
    # Handle None, non-existent values
    if value is None:
        return ""

    # Handle NaN values (both float NaN and string 'nan')
    if isinstance(value, float):
        if str(value).lower() == "nan":
            return ""
    elif isinstance(value, str):
        if value.lower() == "nan":
            return ""

    # Convert to string and strip whitespace
    return str(value).strip()


def get_department(apollo_contact_data: Dict[str, Any]) -> str:
    """获取部门"""
    results = []
    value_to_department = {d.get("value"): d for d in departments_list}
    departments = eval(apollo_contact_data.get("departments", "[]"))
    if departments and len(departments) > 0:
        for department_value in departments:
            department = value_to_department.get(department_value)
            if not department:
                continue
            results.append(department.get("label"))

    return ", ".join(results) if results else ""


async def transform_apollo_contact_data_to_zoho_contact_data(
    apollo_contact_data: Dict[str, Any],
    default_data: dict,
):
    try:
        """将 Apollo 的联系人数据转换为 Zoho 的联系人数据"""
        default_data_extended = {
            **default_data,
            **{
                "Newsletter_Signup": "-None-",
                "Country_Territory": "-None-",
                "Exchange_Rate": 1,
            },
        }

        # Safely get values with defaults for non-existent keys
        temp_data = {
            "First_Name": safe_str(apollo_contact_data.get("first_name", "")),
            "Last_Name": safe_str(apollo_contact_data.get("last_name", "")),
            "Email": safe_str(apollo_contact_data.get("email", "")),
            "Phone": safe_str(apollo_contact_data.get("phone_number", "")),
            "Title": safe_str(apollo_contact_data.get("title", "")),
            "Department": get_department(apollo_contact_data),
            "Mobile": safe_str(apollo_contact_data.get("mobile_number", "")),
            "Function_Type": "Business Management",  # need llm
            "Secondary_Email": "",
            "LinkedIn": safe_str(apollo_contact_data.get("linkedin_url", "")),
            "Currency": "USD",
            "Labels": "Sales Agent",
            "Associated_Account_Type": "-None-",  # need llm
            "Associated_Industry": "-None-",  # need llm
            "Lead_Source": "Linkedin",  # 实际上应该是 Agent
            "Twitter": get_twitter_name(safe_str(apollo_contact_data.get("twitter_url", ""))),
            "Facebook": safe_str(apollo_contact_data.get("facebook_url", "")),
            "Region": safe_str(apollo_contact_data.get("country", "")),
            "Rec_Reason": safe_str(apollo_contact_data.get("thinking", "")),
            "Level": safe_str(apollo_contact_data.get("priority", "medium")),
        }
        summarized_data = await summarize_apollo_user_info(apollo_contact_data)

        # Combine all data
        result_data = {**default_data_extended, **temp_data, **summarized_data}

        # Final check for any NaN values that might have been missed
        for key, value in result_data.items():
            if isinstance(value, float) and str(value).lower() == "nan":
                result_data[key] = ""
            elif isinstance(value, str) and value.lower() == "nan":
                result_data[key] = ""

        return result_data
    except Exception as e:
        logging.error(f"转换数据失败: {e}")
        return {}


async def transform_apollo_contacts_data_to_zoho_contacts_data(
    apollo_contacts_data: List[Dict[str, Any]],
    default_data: dict,
):
    """将 Apollo 的联系人数据转换为 Zoho 的联系人数据"""
    zoho_contacts_data = []
    if apollo_contacts_data is None or len(apollo_contacts_data) == 0:
        return zoho_contacts_data

    for contact in apollo_contacts_data:
        # Process each contact and ensure NaN values are handled
        processed_contact = await transform_apollo_contact_data_to_zoho_contact_data(contact, default_data)
        zoho_contacts_data.append(processed_contact)

    return zoho_contacts_data


async def test_data():
    user_info = {
        "id": "54a4c4097468693676168261",
        "first_name": "Kate",
        "last_name": "Brittain",
        "name": "Kate Brittain",
        "linkedin_url": "http://www.linkedin.com/in/kate-brittain-14911076",
        "title": "Assistant Manager",
        "email_status": "verified",
        "photo_url": "https://media.licdn.com/dms/image/v2/C4E03AQGTjCGm4o0XCA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1516569393130?e=**********&v=beta&t=991gaR14Go1Yrd9ZaEbl3XrkfuYnL-ScdbhfMarUifY",
        "twitter_url": "",
        "github_url": "",
        "facebook_url": "",
        "extrapolated_email_confidence": "",
        "headline": "Assistant Manager",
        "email": "<EMAIL>",
        "organization_id": "54a11d8569702d7fe6152f01",
        "state": "Queensland",
        "city": "Brisbane",
        "country": "Australia",
        "departments": "['master_operations', 'master_sales']",
        "subdepartments": "['customer_service_support', 'sales']",
        "seniority": "manager",
        "functions": [],
        "intent_strength": "",
        "show_intent": False,
        "email_domain_catchall": False,
        "revealed_for_current_team": True,
        "personal_emails": [],
        "about": "I am a passionate  driven individual looking for that next challenge to further my career.",  # noqa: E501
        "thinking": "她是销售骨干，并担任助理经理，具备销售和管理经验，工作积极主动，能影响采购或设备引进决策，非常适合作为物联网和边缘计算产品的切入点。LinkedIn活跃度较高，有进一步沟通的空间。",  # noqa: E501
    }
    formatted_data = await transform_apollo_contact_data_to_zoho_contact_data(user_info, {})
    logging.info(formatted_data)


if __name__ == "__main__":
    import asyncio

    # 运行异步函数
    asyncio.run(test_data())
